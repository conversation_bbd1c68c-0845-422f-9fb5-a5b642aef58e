#!/usr/bin/env python3
"""
Test camera built-in MacBook - Bỏ qua camera Sony
"""

import cv2
import time

def find_builtin_camera():
    """Tìm camera built-in bằng nhiều cách"""
    print("🔍 TÌM CAMERA BUILT-IN MACBOOK")
    print("=" * 50)
    
    # Phương pháp 1: Thử AVFoundation backend trực tiếp
    print("\n📹 Phương pháp 1: AVFoundation Backend")
    for camera_id in range(5):
        print(f"  Test camera {camera_id} với AVFoundation...", end=" ")
        try:
            cap = cv2.VideoCapture(camera_id, cv2.CAP_AVFOUNDATION)
            if cap.isOpened():
                ret, frame = cap.read()
                if ret and frame is not None:
                    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                    print(f"✅ OK - {width}x{height}")
                    
                    # Test xem có phải camera built-in không
                    if test_camera_live(cap, camera_id, "AVFoundation"):
                        return camera_id, cv2.CAP_AVFOUNDATION
                else:
                    print("❌ Không đọc được frame")
                cap.release()
            else:
                print("❌ Không mở được")
        except Exception as e:
            print(f"❌ Lỗi: {e}")
    
    # Phương pháp 2: Thử backend mặc định
    print("\n📹 Phương pháp 2: Backend mặc định")
    for camera_id in range(5):
        print(f"  Test camera {camera_id} mặc định...", end=" ")
        try:
            cap = cv2.VideoCapture(camera_id)
            if cap.isOpened():
                ret, frame = cap.read()
                if ret and frame is not None:
                    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                    print(f"✅ OK - {width}x{height}")
                    
                    # Test xem có phải camera built-in không
                    if test_camera_live(cap, camera_id, "Default"):
                        return camera_id, None
                else:
                    print("❌ Không đọc được frame")
                cap.release()
            else:
                print("❌ Không mở được")
        except Exception as e:
            print(f"❌ Lỗi: {e}")
    
    return None, None

def test_camera_live(cap, camera_id, backend_name):
    """Test camera với live preview ngắn"""
    print(f"\n🎥 TEST LIVE CAMERA {camera_id} ({backend_name})")
    print("📋 Nhấn 'y' nếu thấy video, 'n' nếu không, 'q' để thoát")
    
    frame_count = 0
    start_time = time.time()
    
    while True:
        ret, frame = cap.read()
        if not ret:
            print("❌ Không đọc được frame!")
            return False
        
        frame_count += 1
        
        # Flip frame để giống như nhìn gương
        frame = cv2.flip(frame, 1)
        
        # Thêm thông tin
        cv2.putText(frame, f"Camera {camera_id} ({backend_name}) - Frame: {frame_count}", 
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        cv2.putText(frame, "Nhan 'y' neu thay video, 'n' neu khong", 
                   (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(frame, "Nhan 'q' de thoat", 
                   (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        cv2.imshow(f'Test Camera {camera_id} ({backend_name})', frame)
        
        key = cv2.waitKey(1) & 0xFF
        if key == ord('y') or key == ord('Y'):
            cv2.destroyAllWindows()
            print("✅ Camera này hoạt động tốt!")
            return True
        elif key == ord('n') or key == ord('N'):
            cv2.destroyAllWindows()
            print("❌ Camera này không phù hợp")
            return False
        elif key == ord('q') or key == ord('Q'):
            cv2.destroyAllWindows()
            print("🚫 Đã thoát test")
            return False
        
        # Tự động thoát sau 10 giây
        if time.time() - start_time > 10:
            cv2.destroyAllWindows()
            print("⏰ Timeout - tự động thoát")
            return False

def main():
    """Chương trình chính"""
    print("🎭 TÌM VÀ TEST CAMERA BUILT-IN")
    print("=" * 60)
    
    camera_id, backend = find_builtin_camera()
    
    if camera_id is not None:
        print(f"\n🎉 ĐÃ TÌM THẤY CAMERA BUILT-IN!")
        print(f"📹 Camera ID: {camera_id}")
        print(f"🔧 Backend: {backend if backend else 'Default'}")
        
        # Test lâu dài
        print(f"\n🚀 CHẠY TEST DÀI HẠN...")
        if backend:
            cap = cv2.VideoCapture(camera_id, backend)
        else:
            cap = cv2.VideoCapture(camera_id)
        
        if cap.isOpened():
            print("📷 Camera sẵn sàng! Nhấn ESC để thoát.")
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    print("❌ Mất kết nối camera!")
                    break
                
                # Flip frame
                frame = cv2.flip(frame, 1)
                
                # Thêm thông tin
                cv2.putText(frame, f"Camera Built-in {camera_id} - Nhan ESC de thoat", 
                           (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                
                cv2.imshow('Camera Built-in MacBook', frame)
                
                if cv2.waitKey(1) & 0xFF == 27:  # ESC
                    break
            
            cap.release()
            cv2.destroyAllWindows()
            print("✅ Test hoàn tất!")
        else:
            print("❌ Không thể mở camera cho test dài hạn!")
    else:
        print("\n❌ KHÔNG TÌM THẤY CAMERA BUILT-IN NÀO!")
        print("💡 Gợi ý:")
        print("  • Kiểm tra xem có ứng dụng nào đang sử dụng camera")
        print("  • Thử rút tất cả camera USB ngoài")
        print("  • Khởi động lại MacBook")

if __name__ == "__main__":
    main()
