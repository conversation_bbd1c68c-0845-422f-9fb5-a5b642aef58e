from huggingface_hub import snapshot_download
from insightface.app import FaceAnalysis
import numpy as np
import cv2
import os

# Tải model từ Hugging Face
print("Đang tải model AuraFace...")
snapshot_download(
    "fal/AuraFace-v1",
    local_dir="models/auraface",
)

# Khởi tạo FaceAnalysis với AuraFace, dùng CPU
face_app = FaceAnalysis(
    name="auraface",
    providers=["CPUExecutionProvider"],  # Chỉ CPU cho Mac Intel
    root=".",
)

# Chuẩn bị model (có thể mất thời gian lần đầu)
print("Đang khởi tạo model...")
face_app.prepare(ctx_id=0, det_size=(640, 640))

def list_cameras():
    """Liệt kê tất cả camera có sẵn"""
    print("Đang tìm kiếm camera...")
    cameras = []
    for i in range(10):  # Kiểm tra tối đa 10 camera
        cap = cv2.VideoCapture(i)
        if cap.isOpened():
            ret, frame = cap.read()
            if ret:
                # Lấy thông tin camera
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                fps = int(cap.get(cv2.CAP_PROP_FPS))
                cameras.append({
                    'id': i,
                    'width': width,
                    'height': height,
                    'fps': fps
                })
            cap.release()
    return cameras

def select_camera():
    """Cho phép người dùng chọn camera"""
    cameras = list_cameras()
    
    if not cameras:
        print("❌ Không tìm thấy camera nào!")
        return None
    
    print(f"\n📹 Tìm thấy {len(cameras)} camera:")
    for i, cam_info in enumerate(cameras):
        print(f"  {i+1}. Camera {cam_info['id']} - {cam_info['width']}x{cam_info['height']} @ {cam_info['fps']}fps")
    
    while True:
        try:
            choice = input(f"\n🎯 Chọn camera (1-{len(cameras)}): ")
            choice_idx = int(choice) - 1
            if 0 <= choice_idx < len(cameras):
                selected_cam = cameras[choice_idx]
                print(f"✅ Đã chọn Camera {selected_cam['id']}")
                return selected_cam['id']
            else:
                print(f"❌ Vui lòng chọn từ 1 đến {len(cameras)}")
        except ValueError:
            print("❌ Vui lòng nhập số!")

def calculate_similarity(embedding1, embedding2):
    """Tính độ tương đồng cosine giữa hai embedding"""
    return np.dot(embedding1, embedding2)

def register_face():
    """Đăng ký khuôn mặt của bạn làm reference"""
    print("\n" + "="*50)
    print("📝 ĐĂNG KÝ KHUÔN MẶT CỦA BẠN")
    print("="*50)
    
    # Chọn camera
    camera_id = select_camera()
    if camera_id is None:
        return None
    
    print("\n📋 Hướng dẫn:")
    print("  • Nhấn SPACE để chụp ảnh đăng ký")
    print("  • Nhấn ESC để thoát")
    print("  • Đảm bảo khuôn mặt rõ ràng và đủ sáng")
    
    cap = cv2.VideoCapture(camera_id)
    if not cap.isOpened():
        print(f"❌ Không thể mở camera {camera_id}!")
        return None
    
    # Cài đặt độ phân giải camera
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    
    registered_embedding = None
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
            
        # Flip frame để giống như nhìn gương
        frame = cv2.flip(frame, 1)
        
        # Phát hiện khuôn mặt
        faces = face_app.get(frame)
        
        # Vẽ khung khuôn mặt
        for face in faces:
            bbox = face.bbox.astype(int)
            cv2.rectangle(frame, (bbox[0], bbox[1]), (bbox[2], bbox[3]), (0, 255, 0), 2)
            cv2.putText(frame, f"Confidence: {face.det_score:.2f}", 
                       (bbox[0], bbox[1]-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        
        # Hiển thị hướng dẫn
        cv2.putText(frame, "Nhan SPACE de dang ky, ESC de thoat", 
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(frame, f"So khuon mat: {len(faces)}", 
                   (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        
        cv2.imshow('Dang ky khuon mat', frame)
        
        key = cv2.waitKey(1) & 0xFF
        if key == 27:  # ESC
            print("❌ Đã hủy đăng ký")
            break
        elif key == 32:  # SPACE
            if faces:
                registered_embedding = faces[0].normed_embedding
                print("✅ Đã đăng ký khuôn mặt thành công!")
                # Lưu embedding vào file
                np.save('registered_face.npy', registered_embedding)
                print("💾 Đã lưu thông tin khuôn mặt vào file")
                break
            else:
                print("❌ Không phát hiện khuôn mặt! Thử lại.")
    
    cap.release()
    cv2.destroyAllWindows()
    return registered_embedding

def recognize_face_realtime(registered_embedding, threshold=0.6):
    """Nhận diện khuôn mặt theo thời gian thực"""
    print("\n" + "="*50)
    print("🔍 NHẬN DIỆN KHUÔN MẶT THEO THỜI GIAN THỰC")
    print("="*50)
    
    # Chọn camera
    camera_id = select_camera()
    if camera_id is None:
        return
    
    print(f"\n📋 Ngưỡng nhận diện: {threshold:.2f}")
    print("📋 Hướng dẫn:")
    print("  • Nhấn ESC để thoát")
    print("  • Xanh lá = Đúng người")
    print("  • Đỏ = Sai người")
    
    cap = cv2.VideoCapture(camera_id)
    if not cap.isOpened():
        print(f"❌ Không thể mở camera {camera_id}!")
        return
    
    # Cài đặt độ phân giải camera
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
            
        # Flip frame để giống như nhìn gương
        frame = cv2.flip(frame, 1)
        
        # Phát hiện khuôn mặt
        faces = face_app.get(frame)
        
        for face in faces:
            bbox = face.bbox.astype(int)
            
            # Tính độ tương đồng
            similarity = calculate_similarity(registered_embedding, face.normed_embedding)
            
            # Xác định màu sắc và nhãn
            if similarity >= threshold:
                color = (0, 255, 0)  # Xanh lá - Đúng người
                label = f"DUNG NGUOI! ({similarity:.3f})"
                status = "✅ AUTHORIZED"
            else:
                color = (0, 0, 255)  # Đỏ - Sai người
                label = f"SAI NGUOI! ({similarity:.3f})"
                status = "❌ UNAUTHORIZED"
            
            # Vẽ khung và nhãn
            cv2.rectangle(frame, (bbox[0], bbox[1]), (bbox[2], bbox[3]), color, 2)
            cv2.putText(frame, label, (bbox[0], bbox[1]-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
            
            # Hiển thị thông tin thêm
            cv2.putText(frame, f"Confidence: {face.det_score:.2f}", 
                       (bbox[0], bbox[3]+20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            cv2.putText(frame, f"Age: {face.age:.0f}, Gender: {'M' if face.gender == 1 else 'F'}", 
                       (bbox[0], bbox[3]+40), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            
            # In trạng thái ra console
            print(f"\r{status} - Similarity: {similarity:.3f}", end="", flush=True)
        
        # Hiển thị hướng dẫn
        cv2.putText(frame, f"Nguong: {threshold:.2f} - ESC de thoat", 
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(frame, f"So khuon mat: {len(faces)}", 
                   (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        
        cv2.imshow('Nhan dien khuon mat', frame)
        
        if cv2.waitKey(1) & 0xFF == 27:  # ESC
            break
    
    cap.release()
    cv2.destroyAllWindows()
    print("\n✅ Đã thoát chương trình nhận diện")

def main():
    """Chương trình chính"""
    print("🎭 CHƯƠNG TRÌNH NHẬN DIỆN KHUÔN MẶT AURAFACE")
    print("=" * 60)
    
    # Kiểm tra xem đã có embedding đăng ký chưa
    if os.path.exists('registered_face.npy'):
        print("📁 Tìm thấy khuôn mặt đã đăng ký.")
        choice = input("🤔 Bạn muốn: (1) Sử dụng khuôn mặt đã đăng ký, (2) Đăng ký lại? [1/2]: ")
        
        if choice == '2':
            registered_embedding = register_face()
        else:
            registered_embedding = np.load('registered_face.npy')
            print("✅ Đã tải khuôn mặt đã đăng ký.")
    else:
        print("📝 Chưa có khuôn mặt đăng ký. Cần đăng ký trước.")
        registered_embedding = register_face()
    
    if registered_embedding is not None:
        # Cho phép điều chỉnh ngưỡng
        threshold_input = input("\n🎯 Nhập ngưỡng nhận diện (0.0-1.0, khuyến nghị 0.6): ")
        threshold = float(threshold_input) if threshold_input else 0.6
        recognize_face_realtime(registered_embedding, threshold)
    else:
        print("❌ Không thể đăng ký khuôn mặt. Thoát chương trình.")

if __name__ == "__main__":
    main()
