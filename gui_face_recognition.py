import tkinter as tk
from tkinter import ttk, messagebox
from huggingface_hub import snapshot_download
from insightface.app import FaceAnalysis
import numpy as np
import cv2
import os
import threading
from PIL import Image, ImageTk

class FaceRecognitionGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("🎭 AuraFace - <PERSON><PERSON>ậ<PERSON> Mặt")
        self.root.geometry("800x600")
        
        # Variables
        self.face_app = None
        self.cameras = []
        self.selected_camera = None
        self.registered_embedding = None
        self.cap = None
        self.is_running = False
        
        # Setup GUI
        self.setup_gui()
        
        # Initialize model in background
        self.init_model()
    
    def setup_gui(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="🎭 AuraFace - <PERSON><PERSON>ậ<PERSON> Mặt", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Camera selection frame
        camera_frame = ttk.LabelFrame(main_frame, text="📹 Chọn Camera", padding="10")
        camera_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Camera list
        self.camera_var = tk.StringVar()
        self.camera_combo = ttk.Combobox(camera_frame, textvariable=self.camera_var, 
                                        state="readonly", width=50)
        self.camera_combo.grid(row=0, column=0, padx=(0, 10))
        
        # Refresh camera button
        refresh_btn = ttk.Button(camera_frame, text="🔄 Làm mới", 
                               command=self.refresh_cameras)
        refresh_btn.grid(row=0, column=1)
        
        # Status label
        self.status_label = ttk.Label(main_frame, text="⏳ Đang khởi tạo model...", 
                                     font=("Arial", 10))
        self.status_label.grid(row=2, column=0, columnspan=2, pady=(0, 10))
        
        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=3, column=0, columnspan=2, pady=(0, 10))
        
        # Register face button
        self.register_btn = ttk.Button(buttons_frame, text="📝 Đăng Ký Khuôn Mặt", 
                                      command=self.register_face, state="disabled")
        self.register_btn.grid(row=0, column=0, padx=(0, 10))
        
        # Start recognition button
        self.recognize_btn = ttk.Button(buttons_frame, text="🔍 Bắt Đầu Nhận Diện", 
                                       command=self.start_recognition, state="disabled")
        self.recognize_btn.grid(row=0, column=1, padx=(0, 10))
        
        # Stop button
        self.stop_btn = ttk.Button(buttons_frame, text="⏹️ Dừng", 
                                  command=self.stop_recognition, state="disabled")
        self.stop_btn.grid(row=0, column=2)
        
        # Settings frame
        settings_frame = ttk.LabelFrame(main_frame, text="⚙️ Cài Đặt", padding="10")
        settings_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Threshold setting
        ttk.Label(settings_frame, text="Ngưỡng nhận diện:").grid(row=0, column=0, sticky=tk.W)
        self.threshold_var = tk.DoubleVar(value=0.6)
        threshold_scale = ttk.Scale(settings_frame, from_=0.3, to=0.9, 
                                   variable=self.threshold_var, orient=tk.HORIZONTAL)
        threshold_scale.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0))
        
        self.threshold_label = ttk.Label(settings_frame, text="0.60")
        self.threshold_label.grid(row=0, column=2, padx=(10, 0))
        
        # Update threshold label
        threshold_scale.configure(command=self.update_threshold_label)
        
        # Video frame
        self.video_frame = ttk.LabelFrame(main_frame, text="📺 Video", padding="10")
        self.video_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Video label
        self.video_label = ttk.Label(self.video_frame, text="Chưa có video", 
                                    background="black", foreground="white")
        self.video_label.grid(row=0, column=0)
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(5, weight=1)
        settings_frame.columnconfigure(1, weight=1)
        
        # Load cameras
        self.refresh_cameras()
    
    def init_model(self):
        """Initialize AuraFace model in background"""
        def load_model():
            try:
                self.status_label.config(text="⏳ Đang tải model AuraFace...")
                
                # Download model
                snapshot_download(
                    "fal/AuraFace-v1",
                    local_dir="models/auraface",
                )
                
                # Initialize FaceAnalysis
                self.face_app = FaceAnalysis(
                    name="auraface",
                    providers=["CPUExecutionProvider"],
                    root=".",
                )
                
                self.status_label.config(text="⏳ Đang khởi tạo model...")
                self.face_app.prepare(ctx_id=0, det_size=(640, 640))
                
                # Check for existing registered face
                if os.path.exists('registered_face.npy'):
                    self.registered_embedding = np.load('registered_face.npy')
                    self.status_label.config(text="✅ Sẵn sàng! Đã có khuôn mặt đăng ký.")
                    self.recognize_btn.config(state="normal")
                else:
                    self.status_label.config(text="✅ Sẵn sàng! Cần đăng ký khuôn mặt trước.")
                
                self.register_btn.config(state="normal")
                
            except Exception as e:
                self.status_label.config(text=f"❌ Lỗi: {str(e)}")
                messagebox.showerror("Lỗi", f"Không thể khởi tạo model: {str(e)}")
        
        # Run in background thread
        threading.Thread(target=load_model, daemon=True).start()
    
    def refresh_cameras(self):
        """Refresh camera list"""
        self.cameras = []
        self.camera_combo['values'] = []
        
        def scan_cameras():
            cameras = []
            for i in range(10):
                cap = cv2.VideoCapture(i)
                if cap.isOpened():
                    ret, frame = cap.read()
                    if ret:
                        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                        fps = int(cap.get(cv2.CAP_PROP_FPS))
                        cameras.append({
                            'id': i,
                            'name': f"Camera {i} - {width}x{height} @ {fps}fps",
                            'width': width,
                            'height': height,
                            'fps': fps
                        })
                    cap.release()
            
            # Update GUI in main thread
            self.root.after(0, self.update_camera_list, cameras)
        
        threading.Thread(target=scan_cameras, daemon=True).start()
    
    def update_camera_list(self, cameras):
        """Update camera list in GUI"""
        self.cameras = cameras
        camera_names = [cam['name'] for cam in cameras]
        self.camera_combo['values'] = camera_names
        
        if cameras:
            self.camera_combo.current(0)
            self.selected_camera = cameras[0]['id']
        else:
            messagebox.showwarning("Cảnh báo", "Không tìm thấy camera nào!")
    
    def update_threshold_label(self, value):
        """Update threshold label"""
        self.threshold_label.config(text=f"{float(value):.2f}")
    
    def register_face(self):
        """Register face"""
        if not self.face_app:
            messagebox.showerror("Lỗi", "Model chưa được khởi tạo!")
            return
        
        if not self.cameras:
            messagebox.showerror("Lỗi", "Không có camera nào!")
            return
        
        # Get selected camera
        selected_idx = self.camera_combo.current()
        if selected_idx < 0:
            messagebox.showerror("Lỗi", "Vui lòng chọn camera!")
            return
        
        self.selected_camera = self.cameras[selected_idx]['id']
        
        # Start registration window
        self.start_registration()
    
    def start_registration(self):
        """Start face registration"""
        reg_window = tk.Toplevel(self.root)
        reg_window.title("📝 Đăng Ký Khuôn Mặt")
        reg_window.geometry("800x600")
        
        # Instructions
        instructions = ttk.Label(reg_window, 
                               text="📋 Hướng dẫn:\n• Nhìn thẳng vào camera\n• Nhấn SPACE để chụp\n• Nhấn ESC để thoát",
                               font=("Arial", 12))
        instructions.pack(pady=10)
        
        # Video label for registration
        reg_video_label = ttk.Label(reg_window, text="Đang khởi động camera...", 
                                   background="black", foreground="white")
        reg_video_label.pack(expand=True, fill=tk.BOTH, padx=10, pady=10)
        
        # Start camera for registration
        self.start_registration_camera(reg_video_label, reg_window)
    
    def start_registration_camera(self, video_label, window):
        """Start camera for registration"""
        cap = cv2.VideoCapture(self.selected_camera)
        if not cap.isOpened():
            messagebox.showerror("Lỗi", f"Không thể mở camera {self.selected_camera}!")
            window.destroy()
            return
        
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        
        def update_frame():
            if not window.winfo_exists():
                cap.release()
                return
            
            ret, frame = cap.read()
            if ret:
                frame = cv2.flip(frame, 1)
                faces = self.face_app.get(frame)
                
                # Draw face rectangles
                for face in faces:
                    bbox = face.bbox.astype(int)
                    cv2.rectangle(frame, (bbox[0], bbox[1]), (bbox[2], bbox[3]), (0, 255, 0), 2)
                    cv2.putText(frame, f"Confidence: {face.det_score:.2f}", 
                               (bbox[0], bbox[1]-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                
                # Add instructions
                cv2.putText(frame, "Nhan SPACE de dang ky, ESC de thoat", 
                           (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                cv2.putText(frame, f"So khuon mat: {len(faces)}", 
                           (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
                
                # Convert to PIL Image
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                pil_image = Image.fromarray(frame_rgb)
                pil_image = pil_image.resize((640, 480), Image.Resampling.LANCZOS)
                photo = ImageTk.PhotoImage(pil_image)
                
                video_label.configure(image=photo, text="")
                video_label.image = photo
            
            # Schedule next frame
            window.after(33, update_frame)  # ~30 FPS
        
        def on_key(event):
            if event.keysym == 'space':
                ret, frame = cap.read()
                if ret:
                    frame = cv2.flip(frame, 1)
                    faces = self.face_app.get(frame)
                    if faces:
                        self.registered_embedding = faces[0].normed_embedding
                        np.save('registered_face.npy', self.registered_embedding)
                        messagebox.showinfo("Thành công", "✅ Đã đăng ký khuôn mặt thành công!")
                        self.status_label.config(text="✅ Sẵn sàng! Đã có khuôn mặt đăng ký.")
                        self.recognize_btn.config(state="normal")
                        cap.release()
                        window.destroy()
                    else:
                        messagebox.showwarning("Cảnh báo", "❌ Không phát hiện khuôn mặt!")
            elif event.keysym == 'Escape':
                cap.release()
                window.destroy()
        
        window.bind('<KeyPress>', on_key)
        window.focus_set()
        update_frame()
    
    def start_recognition(self):
        """Start face recognition"""
        if not self.registered_embedding is not None:
            messagebox.showerror("Lỗi", "Chưa có khuôn mặt đăng ký!")
            return
        
        if not self.cameras:
            messagebox.showerror("Lỗi", "Không có camera nào!")
            return
        
        # Get selected camera
        selected_idx = self.camera_combo.current()
        if selected_idx < 0:
            messagebox.showerror("Lỗi", "Vui lòng chọn camera!")
            return
        
        self.selected_camera = self.cameras[selected_idx]['id']
        
        # Start recognition
        self.is_running = True
        self.register_btn.config(state="disabled")
        self.recognize_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        
        self.start_recognition_camera()
    
    def start_recognition_camera(self):
        """Start camera for recognition"""
        self.cap = cv2.VideoCapture(self.selected_camera)
        if not self.cap.isOpened():
            messagebox.showerror("Lỗi", f"Không thể mở camera {self.selected_camera}!")
            self.stop_recognition()
            return
        
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        
        self.update_recognition_frame()
    
    def update_recognition_frame(self):
        """Update recognition frame"""
        if not self.is_running or not self.cap:
            return
        
        ret, frame = self.cap.read()
        if ret:
            frame = cv2.flip(frame, 1)
            faces = self.face_app.get(frame)
            threshold = self.threshold_var.get()
            
            for face in faces:
                bbox = face.bbox.astype(int)
                similarity = np.dot(self.registered_embedding, face.normed_embedding)
                
                if similarity >= threshold:
                    color = (0, 255, 0)  # Green
                    label = f"DUNG NGUOI! ({similarity:.3f})"
                else:
                    color = (0, 0, 255)  # Red
                    label = f"SAI NGUOI! ({similarity:.3f})"
                
                cv2.rectangle(frame, (bbox[0], bbox[1]), (bbox[2], bbox[3]), color, 2)
                cv2.putText(frame, label, (bbox[0], bbox[1]-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
                
                cv2.putText(frame, f"Age: {face.age:.0f}, Gender: {'M' if face.gender == 1 else 'F'}", 
                           (bbox[0], bbox[3]+20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            
            # Add info
            cv2.putText(frame, f"Nguong: {threshold:.2f}", 
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            cv2.putText(frame, f"So khuon mat: {len(faces)}", 
                       (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
            
            # Convert to PIL Image
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(frame_rgb)
            pil_image = pil_image.resize((640, 480), Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(pil_image)
            
            self.video_label.configure(image=photo, text="")
            self.video_label.image = photo
        
        # Schedule next frame
        if self.is_running:
            self.root.after(33, self.update_recognition_frame)  # ~30 FPS
    
    def stop_recognition(self):
        """Stop recognition"""
        self.is_running = False
        if self.cap:
            self.cap.release()
            self.cap = None
        
        self.video_label.configure(image="", text="Đã dừng nhận diện")
        self.video_label.image = None
        
        self.register_btn.config(state="normal")
        self.recognize_btn.config(state="normal")
        self.stop_btn.config(state="disabled")

def main():
    root = tk.Tk()
    app = FaceRecognitionGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
