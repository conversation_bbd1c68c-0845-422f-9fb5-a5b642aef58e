import cv2
import subprocess
import sys

def check_opencv_cameras():
    """Kiểm tra camera qua OpenCV"""
    print("🔍 KIỂM TRA CAMERA QUA OPENCV")
    print("=" * 50)
    
    cameras = []
    for i in range(20):  # Kiểm tra từ 0 đến 19
        print(f"Đang kiểm tra camera {i}...", end=" ")
        cap = cv2.VideoCapture(i)
        
        if cap.isOpened():
            ret, frame = cap.read()
            if ret and frame is not None:
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                fps = int(cap.get(cv2.CAP_PROP_FPS))
                backend = cap.getBackendName()
                
                cameras.append({
                    'id': i,
                    'width': width,
                    'height': height,
                    'fps': fps,
                    'backend': backend,
                    'working': True
                })
                print(f"✅ HOẠT ĐỘNG - {width}x{height} @ {fps}fps ({backend})")
            else:
                print("❌ Mở được nhưng không đọc được frame")
            cap.release()
        else:
            print("❌ Không mở được")
    
    return cameras

def check_system_cameras_macos():
    """Kiểm tra camera qua system command trên macOS"""
    print("\n🖥️  KIỂM TRA CAMERA QUA HỆ THỐNG macOS")
    print("=" * 50)
    
    try:
        # Sử dụng system_profiler để lấy thông tin camera
        result = subprocess.run([
            'system_profiler', 'SPCameraDataType'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("📋 Thông tin camera từ hệ thống:")
            print(result.stdout)
        else:
            print("❌ Không thể lấy thông tin camera từ hệ thống")
            
    except Exception as e:
        print(f"❌ Lỗi khi kiểm tra camera hệ thống: {e}")

def check_ffmpeg_cameras():
    """Kiểm tra camera qua ffmpeg"""
    print("\n🎥 KIỂM TRA CAMERA QUA FFMPEG")
    print("=" * 50)
    
    try:
        # List video devices
        result = subprocess.run([
            'ffmpeg', '-f', 'avfoundation', '-list_devices', 'true', '-i', ''
        ], capture_output=True, text=True, timeout=10)
        
        print("📋 Danh sách thiết bị video:")
        print(result.stderr)  # ffmpeg outputs device list to stderr
        
    except FileNotFoundError:
        print("❌ ffmpeg không được cài đặt")
    except Exception as e:
        print(f"❌ Lỗi khi kiểm tra ffmpeg: {e}")

def test_camera_detailed(camera_id):
    """Test chi tiết một camera cụ thể"""
    print(f"\n🔬 TEST CHI TIẾT CAMERA {camera_id}")
    print("=" * 50)
    
    cap = cv2.VideoCapture(camera_id)
    
    if not cap.isOpened():
        print(f"❌ Không thể mở camera {camera_id}")
        return False
    
    # Lấy thông tin chi tiết
    properties = {
        'CAP_PROP_FRAME_WIDTH': cv2.CAP_PROP_FRAME_WIDTH,
        'CAP_PROP_FRAME_HEIGHT': cv2.CAP_PROP_FRAME_HEIGHT,
        'CAP_PROP_FPS': cv2.CAP_PROP_FPS,
        'CAP_PROP_FOURCC': cv2.CAP_PROP_FOURCC,
        'CAP_PROP_BRIGHTNESS': cv2.CAP_PROP_BRIGHTNESS,
        'CAP_PROP_CONTRAST': cv2.CAP_PROP_CONTRAST,
        'CAP_PROP_SATURATION': cv2.CAP_PROP_SATURATION,
        'CAP_PROP_AUTO_EXPOSURE': cv2.CAP_PROP_AUTO_EXPOSURE,
    }
    
    print(f"📊 Thông tin camera {camera_id}:")
    for prop_name, prop_id in properties.items():
        try:
            value = cap.get(prop_id)
            print(f"  {prop_name}: {value}")
        except:
            print(f"  {prop_name}: Không hỗ trợ")
    
    # Test đọc frame
    print(f"\n📸 Test đọc frame:")
    for i in range(5):
        ret, frame = cap.read()
        if ret:
            print(f"  Frame {i+1}: ✅ Thành công - Shape: {frame.shape}")
        else:
            print(f"  Frame {i+1}: ❌ Thất bại")
    
    cap.release()
    return True

def main():
    print("🎭 KIỂM TRA TOÀN BỘ CAMERA TRÊN HỆ THỐNG")
    print("=" * 60)
    
    # 1. Kiểm tra OpenCV cameras
    opencv_cameras = check_opencv_cameras()
    
    # 2. Kiểm tra system cameras (macOS)
    if sys.platform == "darwin":  # macOS
        check_system_cameras_macos()
    
    # 3. Kiểm tra ffmpeg cameras
    check_ffmpeg_cameras()
    
    # 4. Tóm tắt kết quả
    print(f"\n📋 TÓM TẮT KẾT QUẢ")
    print("=" * 50)
    
    if opencv_cameras:
        print(f"✅ Tìm thấy {len(opencv_cameras)} camera hoạt động qua OpenCV:")
        for cam in opencv_cameras:
            print(f"  📹 Camera {cam['id']}: {cam['width']}x{cam['height']} @ {cam['fps']}fps ({cam['backend']})")
        
        # Test chi tiết camera đầu tiên
        if opencv_cameras:
            test_camera_detailed(opencv_cameras[0]['id'])
            
    else:
        print("❌ Không tìm thấy camera nào hoạt động qua OpenCV")
    
    # 5. Gợi ý khắc phục
    print(f"\n💡 GỢI Ý KHẮC PHỤC")
    print("=" * 50)
    print("1. Kiểm tra quyền truy cập camera trong System Preferences > Security & Privacy > Camera")
    print("2. Đảm bảo không có ứng dụng nào khác đang sử dụng camera")
    print("3. Thử khởi động lại ứng dụng camera mặc định để test")
    print("4. Nếu dùng camera ngoài, kiểm tra kết nối USB")
    print("5. Thử các camera ID khác nhau (0, 1, 2, ...)")

if __name__ == "__main__":
    main()
