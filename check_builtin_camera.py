#!/usr/bin/env python3
"""
Kiểm tra camera built-in của MacBook
"""

import cv2
import subprocess
import sys

def check_builtin_camera():
    """Tìm camera built-in của MacBook"""
    print("🔍 TÌM CAMERA BUILT-IN MACBOOK")
    print("=" * 50)
    
    # Kiểm tra camera qua system_profiler
    try:
        result = subprocess.run([
            'system_profiler', 'SPCameraDataType'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("📋 Thông tin camera từ hệ thống:")
            lines = result.stdout.split('\n')
            for line in lines:
                if 'Camera' in line or 'FaceTime' in line or 'Built-in' in line:
                    print(f"  {line.strip()}")
        print()
    except Exception as e:
        print(f"❌ Không thể lấy thông tin camera: {e}")
    
    # Test từng camera ID
    builtin_cameras = []
    
    for camera_id in range(10):
        print(f"📹 Test Camera ID {camera_id}...", end=" ")
        
        cap = cv2.VideoCapture(camera_id)
        if cap.isOpened():
            ret, frame = cap.read()
            if ret and frame is not None:
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                fps = int(cap.get(cv2.CAP_PROP_FPS))
                backend = cap.getBackendName()
                
                # Kiểm tra xem có phải camera built-in không
                # Camera built-in thường có tên không chứa "Sony" hoặc "Imaging Edge"
                camera_name = "Unknown"
                try:
                    # Thử lấy tên camera (không phải tất cả backend đều hỗ trợ)
                    pass
                except:
                    pass
                
                # Heuristic: Camera built-in thường là camera đầu tiên không phải Sony
                is_builtin = camera_id > 0 or "Sony" not in str(cap)
                
                camera_info = {
                    'id': camera_id,
                    'width': width,
                    'height': height,
                    'fps': fps,
                    'backend': backend,
                    'is_builtin': is_builtin
                }
                
                if camera_id == 0:
                    print(f"✅ HOẠT ĐỘNG - {width}x{height} @ {fps}fps ({backend}) - CAMERA CHÍNH")
                else:
                    print(f"✅ HOẠT ĐỘNG - {width}x{height} @ {fps}fps ({backend})")
                
                builtin_cameras.append(camera_info)
            else:
                print("❌ Mở được nhưng không đọc được frame")
            cap.release()
        else:
            print("❌ Không mở được")
    
    return builtin_cameras

def test_camera_live(camera_id):
    """Test camera với live preview"""
    print(f"\n🎥 TEST LIVE CAMERA {camera_id}")
    print("=" * 50)
    print("📋 Hướng dẫn:")
    print("  • Nhấn 'q' để thoát")
    print("  • Nhấn 's' để chụp ảnh test")
    
    cap = cv2.VideoCapture(camera_id)
    if not cap.isOpened():
        print(f"❌ Không thể mở camera {camera_id}")
        return False
    
    # Cài đặt độ phân giải
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    
    print(f"📷 Camera {camera_id} đã sẵn sàng. Cửa sổ video sẽ mở...")
    
    frame_count = 0
    while True:
        ret, frame = cap.read()
        if not ret:
            print("❌ Không thể đọc frame!")
            break
        
        frame_count += 1
        
        # Flip frame để giống như nhìn gương
        frame = cv2.flip(frame, 1)
        
        # Thêm thông tin
        cv2.putText(frame, f"Camera {camera_id} - Frame: {frame_count}", 
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        cv2.putText(frame, "Nhan 'q' de thoat, 's' de chup anh", 
                   (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        cv2.imshow(f'Test Camera {camera_id}', frame)
        
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('s'):
            filename = f'test_camera_{camera_id}_frame_{frame_count}.jpg'
            cv2.imwrite(filename, frame)
            print(f"📸 Đã lưu ảnh: {filename}")
    
    cap.release()
    cv2.destroyAllWindows()
    print(f"✅ Test camera {camera_id} hoàn tất!")
    return True

def main():
    """Chương trình chính"""
    print("🎭 TÌM VÀ TEST CAMERA BUILT-IN MACBOOK")
    print("=" * 60)
    
    # Tìm tất cả camera
    cameras = check_builtin_camera()
    
    if not cameras:
        print("❌ Không tìm thấy camera nào!")
        return
    
    print(f"\n📊 TÓM TẮT KẾT QUẢ")
    print("=" * 50)
    print(f"✅ Tìm thấy {len(cameras)} camera:")
    
    for i, cam in enumerate(cameras):
        status = "📷 BUILT-IN" if cam['id'] != 0 else "📹 CAMERA CHÍNH"
        print(f"  {i+1}. Camera {cam['id']}: {cam['width']}x{cam['height']} @ {cam['fps']}fps - {status}")
    
    # Gợi ý camera built-in
    builtin_candidates = [cam for cam in cameras if cam['id'] != 0]
    if builtin_candidates:
        recommended = builtin_candidates[0]
        print(f"\n💡 GỢI Ý: Camera {recommended['id']} có thể là camera built-in")
    else:
        print(f"\n💡 GỢI Ý: Chỉ tìm thấy Camera 0, có thể cần tắt camera Sony để thấy built-in camera")
    
    # Cho phép test camera
    while True:
        try:
            choice = input(f"\n🎯 Chọn camera để test live (1-{len(cameras)}, 0 để thoát): ")
            if choice == '0':
                break
            
            choice_idx = int(choice) - 1
            if 0 <= choice_idx < len(cameras):
                selected_cam = cameras[choice_idx]
                test_camera_live(selected_cam['id'])
            else:
                print(f"❌ Vui lòng chọn từ 1 đến {len(cameras)}")
        except ValueError:
            print("❌ Vui lòng nhập số!")
        except KeyboardInterrupt:
            print("\n👋 Tạm biệt!")
            break

if __name__ == "__main__":
    main()
