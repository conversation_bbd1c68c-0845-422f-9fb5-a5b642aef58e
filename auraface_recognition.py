#!/usr/bin/env python3
"""
🎭 AuraFace - <PERSON>ệ Thống N<PERSON>ận <PERSON> Mặt
Sử dụng AuraFace model từ Hugging Face với khả năng chọn camera
"""

from huggingface_hub import snapshot_download
from insightface.app import FaceAnalysis
import numpy as np
import cv2
import os
import time

class CameraManager:
    """Quản lý camera với khả năng phát hiện và chọn camera"""
    
    def __init__(self):
        self.available_cameras = []
        self.detect_cameras()
    
    def detect_cameras(self):
        """Phát hiện tất cả camera có sẵn"""
        print("🔍 PHÁT HIỆN CAMERA...")
        print("=" * 50)
        
        self.available_cameras = []
        
        # Test các camera ID từ 0-4
        for camera_id in range(5):
            camera_info = self._test_camera(camera_id)
            if camera_info:
                self.available_cameras.append(camera_info)
        
        if self.available_cameras:
            print(f"\n✅ Tìm thấy {len(self.available_cameras)} camera:")
            for i, cam in enumerate(self.available_cameras):
                print(f"  {i+1}. {cam['name']} - {cam['resolution']} @ {cam['fps']}fps")
        else:
            print("❌ Không tìm thấy camera nào!")
    
    def _test_camera(self, camera_id):
        """Test một camera cụ thể với kiểm tra frame thực tế"""
        print(f"  📹 Test Camera {camera_id}...", end=" ")

        # Thử AVFoundation backend trước (tốt hơn cho macOS)
        for backend_name, backend in [("AVFoundation", cv2.CAP_AVFOUNDATION), ("Default", None)]:
            try:
                if backend:
                    cap = cv2.VideoCapture(camera_id, backend)
                else:
                    cap = cv2.VideoCapture(camera_id)

                if cap.isOpened():
                    # Test đọc nhiều frame để đảm bảo camera thực sự hoạt động
                    frame_count = 0
                    for _ in range(5):  # Thử đọc 5 frame
                        ret, frame = cap.read()
                        if ret and frame is not None:
                            frame_count += 1

                    if frame_count >= 3:  # Ít nhất 3/5 frame thành công
                        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                        fps = int(cap.get(cv2.CAP_PROP_FPS))

                        # Xác định tên camera
                        if camera_id == 0:
                            name = "Camera Chính (Sony/External)"
                        elif camera_id == 1:
                            name = "Camera Built-in (MacBook)"
                        else:
                            name = f"Camera {camera_id}"

                        print(f"✅ {name} - {width}x{height} ({frame_count}/5 frames)")
                        cap.release()

                        return {
                            'id': camera_id,
                            'name': name,
                            'resolution': f"{width}x{height}",
                            'fps': fps,
                            'backend': backend,
                            'backend_name': backend_name,
                            'reliability': frame_count / 5.0  # Độ tin cậy
                        }
                    else:
                        print(f"❌ Không ổn định ({frame_count}/5 frames)")
                        cap.release()
                else:
                    if cap.isOpened():
                        cap.release()
            except Exception as e:
                if 'cap' in locals() and cap.isOpened():
                    cap.release()
                continue

        print("❌ Không hoạt động")
        return None
    
    def select_camera(self):
        """Cho phép người dùng chọn camera"""
        if not self.available_cameras:
            print("❌ Không có camera nào để chọn!")
            return None
        
        if len(self.available_cameras) == 1:
            selected = self.available_cameras[0]
            print(f"🎯 Tự động chọn: {selected['name']}")
            return selected
        
        print(f"\n📋 CHỌN CAMERA:")
        print("=" * 50)
        for i, cam in enumerate(self.available_cameras):
            print(f"  {i+1}. {cam['name']} - {cam['resolution']} @ {cam['fps']}fps")
        
        while True:
            try:
                choice = input(f"\n🎯 Chọn camera (1-{len(self.available_cameras)}): ")
                choice_idx = int(choice) - 1
                
                if 0 <= choice_idx < len(self.available_cameras):
                    selected = self.available_cameras[choice_idx]
                    print(f"✅ Đã chọn: {selected['name']}")
                    return selected
                else:
                    print(f"❌ Vui lòng chọn từ 1 đến {len(self.available_cameras)}")
            except ValueError:
                print("❌ Vui lòng nhập số!")
            except KeyboardInterrupt:
                print("\n🚫 Đã hủy chọn camera")
                return None
    
    def open_camera(self, camera_info):
        """Mở camera với thông tin đã chọn"""
        if camera_info['backend']:
            cap = cv2.VideoCapture(camera_info['id'], camera_info['backend'])
        else:
            cap = cv2.VideoCapture(camera_info['id'])
        
        if cap.isOpened():
            # Cài đặt độ phân giải tối ưu
            cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            return cap
        else:
            return None

class AuraFaceRecognizer:
    """Hệ thống nhận diện khuôn mặt sử dụng AuraFace"""
    
    def __init__(self):
        self.face_app = None
        self.camera_manager = CameraManager()
        self.registered_embedding = None
        
    def init_auraface(self):
        """Khởi tạo AuraFace model"""
        print("\n🎭 KHỞI TẠO AURAFACE MODEL")
        print("=" * 50)
        
        print("⏳ Đang tải model từ Hugging Face...")
        snapshot_download(
            "fal/AuraFace-v1",
            local_dir="models/auraface",
        )
        
        print("⏳ Đang khởi tạo FaceAnalysis...")
        self.face_app = FaceAnalysis(
            name="auraface",
            providers=["CPUExecutionProvider"],
            root=".",
        )
        
        print("⏳ Đang chuẩn bị model...")
        self.face_app.prepare(ctx_id=0, det_size=(640, 640))
        
        print("✅ AuraFace đã sẵn sàng!")
    
    def register_face(self, camera_info):
        """Đăng ký khuôn mặt"""
        print(f"\n📝 ĐĂNG KÝ KHUÔN MẶT - {camera_info['name']}")
        print("=" * 50)
        print("📋 Hướng dẫn:")
        print("  • Nhấn SPACE để chụp ảnh đăng ký")
        print("  • Nhấn ESC để thoát")
        print("  • Đảm bảo khuôn mặt rõ ràng và đủ sáng")
        
        cap = self.camera_manager.open_camera(camera_info)
        if not cap:
            print("❌ Không thể mở camera!")
            return None
        
        print("📷 Camera đã sẵn sàng. Cửa sổ video sẽ mở...")
        
        while True:
            ret, frame = cap.read()
            if not ret:
                print("❌ Không thể đọc frame từ camera!")
                break
            
            # Flip frame để giống như nhìn gương
            frame = cv2.flip(frame, 1)
            
            # Phát hiện khuôn mặt
            faces = self.face_app.get(frame)
            
            # Vẽ khung khuôn mặt
            for face in faces:
                bbox = face.bbox.astype(int)
                cv2.rectangle(frame, (bbox[0], bbox[1]), (bbox[2], bbox[3]), (0, 255, 0), 2)
                cv2.putText(frame, f"Confidence: {face.det_score:.2f}", 
                           (bbox[0], bbox[1]-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
            
            # Hiển thị hướng dẫn
            cv2.putText(frame, "Nhan SPACE de dang ky, ESC de thoat", 
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(frame, f"So khuon mat: {len(faces)}", 
                       (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
            
            # Hiển thị frame
            cv2.imshow(f'Dang ky khuon mat - {camera_info["name"]}', frame)
            
            key = cv2.waitKey(1) & 0xFF
            if key == 27:  # ESC
                print("❌ Đã hủy đăng ký")
                break
            elif key == 32:  # SPACE
                if faces:
                    self.registered_embedding = faces[0].normed_embedding
                    print("✅ Đã đăng ký khuôn mặt thành công!")
                    # Lưu embedding vào file
                    np.save('registered_face.npy', self.registered_embedding)
                    print("💾 Đã lưu thông tin khuôn mặt vào file")
                    break
                else:
                    print("❌ Không phát hiện khuôn mặt! Thử lại.")
        
        cap.release()
        cv2.destroyAllWindows()
        return self.registered_embedding

    def recognize_face_realtime(self, camera_info, threshold=0.6):
        """Nhận diện khuôn mặt theo thời gian thực"""
        print(f"\n🔍 NHẬN DIỆN KHUÔN MẶT - {camera_info['name']}")
        print("=" * 50)
        print(f"🎯 Ngưỡng nhận diện: {threshold:.2f}")
        print("📋 Hướng dẫn:")
        print("  • Nhấn ESC để thoát")
        print("  • Nhấn 'r' để điều chỉnh ngưỡng")
        print("  • Xanh lá = Đúng người, Đỏ = Sai người")

        cap = self.camera_manager.open_camera(camera_info)
        if not cap:
            print("❌ Không thể mở camera!")
            return

        print("📷 Camera đã sẵn sàng. Cửa sổ video sẽ mở...")

        while True:
            ret, frame = cap.read()
            if not ret:
                print("❌ Không thể đọc frame từ camera!")
                break

            # Flip frame để giống như nhìn gương
            frame = cv2.flip(frame, 1)

            # Phát hiện khuôn mặt
            faces = self.face_app.get(frame)

            for face in faces:
                bbox = face.bbox.astype(int)

                # Tính độ tương đồng
                similarity = np.dot(self.registered_embedding, face.normed_embedding)

                # Xác định màu sắc và nhãn
                if similarity >= threshold:
                    color = (0, 255, 0)  # Xanh lá - Đúng người
                    label = f"DUNG NGUOI! ({similarity:.3f})"
                    status = "✅ AUTHORIZED"
                else:
                    color = (0, 0, 255)  # Đỏ - Sai người
                    label = f"SAI NGUOI! ({similarity:.3f})"
                    status = "❌ UNAUTHORIZED"

                # Vẽ khung và nhãn
                cv2.rectangle(frame, (bbox[0], bbox[1]), (bbox[2], bbox[3]), color, 2)
                cv2.putText(frame, label, (bbox[0], bbox[1]-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

                # Hiển thị thông tin thêm
                cv2.putText(frame, f"Confidence: {face.det_score:.2f}",
                           (bbox[0], bbox[3]+20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
                cv2.putText(frame, f"Age: {face.age:.0f}, Gender: {'M' if face.gender == 1 else 'F'}",
                           (bbox[0], bbox[3]+40), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

                # In trạng thái ra console
                print(f"\r{status} - Similarity: {similarity:.3f}", end="", flush=True)

            # Hiển thị hướng dẫn
            cv2.putText(frame, f"Nguong: {threshold:.2f} - ESC: thoat, R: thay doi nguong",
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
            cv2.putText(frame, f"So khuon mat: {len(faces)}",
                       (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)

            # Hiển thị frame
            cv2.imshow(f'Nhan dien khuon mat - {camera_info["name"]}', frame)

            key = cv2.waitKey(1) & 0xFF
            if key == 27:  # ESC
                break
            elif key == ord('r') or key == ord('R'):  # R để thay đổi ngưỡng
                print(f"\nNgưỡng hiện tại: {threshold:.2f}")
                try:
                    new_threshold = float(input("Nhập ngưỡng mới (0.0-1.0): "))
                    if 0.0 <= new_threshold <= 1.0:
                        threshold = new_threshold
                        print(f"✅ Đã cập nhật ngưỡng: {threshold:.2f}")
                    else:
                        print("❌ Ngưỡng phải từ 0.0 đến 1.0")
                except ValueError:
                    print("❌ Vui lòng nhập số hợp lệ")

        cap.release()
        cv2.destroyAllWindows()
        print("\n✅ Đã thoát chương trình nhận diện")

    def run(self):
        """Chạy chương trình chính"""
        print("🎭 AURAFACE - HỆ THỐNG NHẬN DIỆN KHUÔN MẶT")
        print("=" * 60)

        # Kiểm tra camera
        if not self.camera_manager.available_cameras:
            print("❌ Không tìm thấy camera nào! Thoát chương trình.")
            return

        # Chọn camera
        selected_camera = self.camera_manager.select_camera()
        if not selected_camera:
            print("❌ Không chọn được camera! Thoát chương trình.")
            return

        # Khởi tạo AuraFace
        self.init_auraface()

        # Kiểm tra xem đã có embedding đăng ký chưa
        if os.path.exists('registered_face.npy'):
            print("\n📁 Tìm thấy khuôn mặt đã đăng ký.")
            choice = input("🤔 Bạn muốn: (1) Sử dụng khuôn mặt đã đăng ký, (2) Đăng ký lại? [1/2]: ")

            if choice == '2':
                self.registered_embedding = self.register_face(selected_camera)
            else:
                self.registered_embedding = np.load('registered_face.npy')
                print("✅ Đã tải khuôn mặt đã đăng ký.")
        else:
            print("\n📝 Chưa có khuôn mặt đăng ký. Cần đăng ký trước.")
            self.registered_embedding = self.register_face(selected_camera)

        if self.registered_embedding is not None:
            # Cho phép điều chỉnh ngưỡng
            threshold_input = input("\n🎯 Nhập ngưỡng nhận diện (0.0-1.0, khuyến nghị 0.6): ")
            try:
                threshold = float(threshold_input) if threshold_input else 0.6
            except ValueError:
                threshold = 0.6
                print("⚠️ Sử dụng ngưỡng mặc định: 0.6")

            self.recognize_face_realtime(selected_camera, threshold)
        else:
            print("❌ Không thể đăng ký khuôn mặt. Thoát chương trình.")

def main():
    """Hàm main"""
    try:
        recognizer = AuraFaceRecognizer()
        recognizer.run()
    except KeyboardInterrupt:
        print("\n\n👋 Tạm biệt!")
    except Exception as e:
        print(f"\n❌ Lỗi: {e}")

if __name__ == "__main__":
    main()
