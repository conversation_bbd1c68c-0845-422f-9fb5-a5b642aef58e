#!/usr/bin/env python3
"""
🎭 AuraFace GUI - <PERSON><PERSON> Thống <PERSON>hận Diệ<PERSON> Mặt với <PERSON>iao <PERSON> Đẹp
Sử dụng AuraFace model từ Hugging Face với giao diện Tkinter
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import cv2
import numpy as np
from PIL import Image, ImageTk
import threading
import time
import os
from huggingface_hub import snapshot_download
from insightface.app import FaceAnalysis

class AuraFaceGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎭 AuraFace - Nhận Diện <PERSON> Mặt")
        self.root.geometry("1200x800")
        self.root.configure(bg='#2c3e50')
        
        # Variables
        self.face_app = None
        self.cap = None
        self.registered_embedding = None
        self.is_running = False
        self.current_frame = None
        self.threshold = 0.6
        
        # Setup GUI
        self.setup_gui()
        self.setup_camera()
        
    def setup_gui(self):
        """<PERSON>hi<PERSON><PERSON> lập giao diện"""
        # Main title
        title_frame = tk.Frame(self.root, bg='#2c3e50')
        title_frame.pack(pady=20)
        
        title_label = tk.Label(title_frame, text="🎭 AuraFace Recognition", 
                              font=('Arial', 24, 'bold'), 
                              fg='#ecf0f1', bg='#2c3e50')
        title_label.pack()
        
        subtitle_label = tk.Label(title_frame, text="Hệ thống nhận diện khuôn mặt thông minh", 
                                 font=('Arial', 12), 
                                 fg='#bdc3c7', bg='#2c3e50')
        subtitle_label.pack()
        
        # Main container
        main_frame = tk.Frame(self.root, bg='#2c3e50')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # Left panel - Video
        left_frame = tk.Frame(main_frame, bg='#34495e', relief=tk.RAISED, bd=2)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        video_label = tk.Label(left_frame, text="📹 Camera Feed", 
                              font=('Arial', 14, 'bold'), 
                              fg='#ecf0f1', bg='#34495e')
        video_label.pack(pady=10)
        
        self.video_canvas = tk.Canvas(left_frame, width=640, height=480, bg='#2c3e50')
        self.video_canvas.pack(pady=10)
        
        # Right panel - Controls
        right_frame = tk.Frame(main_frame, bg='#34495e', relief=tk.RAISED, bd=2)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        right_frame.config(width=300)
        
        # Controls title
        controls_label = tk.Label(right_frame, text="⚙️ Điều Khiển", 
                                 font=('Arial', 14, 'bold'), 
                                 fg='#ecf0f1', bg='#34495e')
        controls_label.pack(pady=15)
        
        # Camera selection
        camera_frame = tk.LabelFrame(right_frame, text="📹 Camera", 
                                    font=('Arial', 10, 'bold'),
                                    fg='#ecf0f1', bg='#34495e')
        camera_frame.pack(fill=tk.X, padx=15, pady=10)
        
        self.camera_var = tk.StringVar(value="Camera Built-in (ID: 1)")
        camera_options = ["Camera Built-in (ID: 1)", "Camera External (ID: 0)"]
        self.camera_combo = ttk.Combobox(camera_frame, textvariable=self.camera_var, 
                                        values=camera_options, state="readonly")
        self.camera_combo.pack(fill=tk.X, padx=10, pady=10)
        self.camera_combo.bind('<<ComboboxSelected>>', self.change_camera)
        
        # Model status
        model_frame = tk.LabelFrame(right_frame, text="🤖 AuraFace Model", 
                                   font=('Arial', 10, 'bold'),
                                   fg='#ecf0f1', bg='#34495e')
        model_frame.pack(fill=tk.X, padx=15, pady=10)
        
        self.model_status = tk.Label(model_frame, text="❌ Chưa khởi tạo", 
                                    font=('Arial', 10), 
                                    fg='#e74c3c', bg='#34495e')
        self.model_status.pack(pady=10)
        
        init_model_btn = tk.Button(model_frame, text="🚀 Khởi Tạo Model", 
                                  command=self.init_model_thread,
                                  bg='#3498db', fg='white', 
                                  font=('Arial', 10, 'bold'),
                                  relief=tk.FLAT, padx=20, pady=5)
        init_model_btn.pack(pady=5)
        
        # Face registration
        register_frame = tk.LabelFrame(right_frame, text="📝 Đăng Ký Khuôn Mặt", 
                                      font=('Arial', 10, 'bold'),
                                      fg='#ecf0f1', bg='#34495e')
        register_frame.pack(fill=tk.X, padx=15, pady=10)
        
        self.register_status = tk.Label(register_frame, text="❌ Chưa đăng ký", 
                                       font=('Arial', 10), 
                                       fg='#e74c3c', bg='#34495e')
        self.register_status.pack(pady=5)
        
        register_btn = tk.Button(register_frame, text="📷 Đăng Ký Ngay", 
                                command=self.register_face,
                                bg='#27ae60', fg='white', 
                                font=('Arial', 10, 'bold'),
                                relief=tk.FLAT, padx=20, pady=5)
        register_btn.pack(pady=5)
        
        load_btn = tk.Button(register_frame, text="📁 Tải Từ File", 
                            command=self.load_registered_face,
                            bg='#f39c12', fg='white', 
                            font=('Arial', 10, 'bold'),
                            relief=tk.FLAT, padx=20, pady=5)
        load_btn.pack(pady=5)
        
        # Recognition settings
        settings_frame = tk.LabelFrame(right_frame, text="🎯 Cài Đặt Nhận Diện", 
                                      font=('Arial', 10, 'bold'),
                                      fg='#ecf0f1', bg='#34495e')
        settings_frame.pack(fill=tk.X, padx=15, pady=10)
        
        threshold_label = tk.Label(settings_frame, text=f"Ngưỡng: {self.threshold:.2f}", 
                                  font=('Arial', 10), 
                                  fg='#ecf0f1', bg='#34495e')
        threshold_label.pack(pady=5)
        
        self.threshold_scale = tk.Scale(settings_frame, from_=0.3, to=0.9, 
                                       resolution=0.05, orient=tk.HORIZONTAL,
                                       command=self.update_threshold,
                                       bg='#34495e', fg='#ecf0f1',
                                       highlightbackground='#34495e')
        self.threshold_scale.set(self.threshold)
        self.threshold_scale.pack(fill=tk.X, padx=10, pady=5)
        
        # Recognition control
        recognition_frame = tk.LabelFrame(right_frame, text="🔍 Nhận Diện", 
                                         font=('Arial', 10, 'bold'),
                                         fg='#ecf0f1', bg='#34495e')
        recognition_frame.pack(fill=tk.X, padx=15, pady=10)
        
        self.recognition_btn = tk.Button(recognition_frame, text="▶️ Bắt Đầu Nhận Diện", 
                                        command=self.toggle_recognition,
                                        bg='#e74c3c', fg='white', 
                                        font=('Arial', 12, 'bold'),
                                        relief=tk.FLAT, padx=20, pady=10)
        self.recognition_btn.pack(pady=10)
        
        # Status
        self.status_label = tk.Label(recognition_frame, text="⏸️ Đã dừng", 
                                    font=('Arial', 10), 
                                    fg='#95a5a6', bg='#34495e')
        self.status_label.pack(pady=5)
        
        # Bottom status bar
        status_frame = tk.Frame(self.root, bg='#34495e', height=30)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        
        self.bottom_status = tk.Label(status_frame, text="🎭 AuraFace sẵn sàng", 
                                     font=('Arial', 10), 
                                     fg='#ecf0f1', bg='#34495e')
        self.bottom_status.pack(side=tk.LEFT, padx=10, pady=5)
        
    def setup_camera(self):
        """Thiết lập camera mặc định - ưu tiên built-in camera"""
        # Thử camera built-in trước (ID: 1)
        try:
            test_cap = cv2.VideoCapture(1)
            if test_cap.isOpened():
                ret, frame = test_cap.read()
                if ret and frame is not None:
                    self.camera_var.set("Camera Built-in (ID: 1)")
                    test_cap.release()
                    self.change_camera()
                    return
            test_cap.release()
        except:
            pass

        # Nếu built-in không hoạt động, thử external
        self.camera_var.set("Camera External (ID: 0)")
        self.change_camera()
        
    def change_camera(self, event=None):
        """Thay đổi camera"""
        if self.cap:
            self.cap.release()
            
        camera_text = self.camera_var.get()
        if "ID: 1" in camera_text:
            camera_id = 1  # Built-in camera
        else:
            camera_id = 0  # External camera
            
        try:
            self.cap = cv2.VideoCapture(camera_id)
            if self.cap.isOpened():
                self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
                self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
                self.bottom_status.config(text=f"📹 Đã kết nối camera ID: {camera_id}")
                self.start_video_feed()
            else:
                messagebox.showerror("Lỗi", f"Không thể mở camera ID: {camera_id}")
        except Exception as e:
            messagebox.showerror("Lỗi", f"Lỗi kết nối camera: {str(e)}")
            
    def start_video_feed(self):
        """Bắt đầu hiển thị video feed"""
        def update_frame():
            if self.cap and self.cap.isOpened():
                ret, frame = self.cap.read()
                if ret:
                    self.current_frame = frame.copy()
                    frame = cv2.flip(frame, 1)  # Mirror effect
                    
                    # Process frame if recognition is running
                    if self.is_running and self.face_app and self.registered_embedding is not None:
                        frame = self.process_recognition(frame)
                    
                    # Convert to display format
                    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    frame_pil = Image.fromarray(frame_rgb)
                    frame_tk = ImageTk.PhotoImage(frame_pil)
                    
                    # Update canvas
                    self.video_canvas.delete("all")
                    self.video_canvas.create_image(320, 240, image=frame_tk)
                    self.video_canvas.image = frame_tk
                    
            self.root.after(30, update_frame)  # ~33 FPS
            
        update_frame()
        
    def init_model_thread(self):
        """Khởi tạo model trong thread riêng"""
        def init_model():
            try:
                self.model_status.config(text="⏳ Đang tải model...", fg='#f39c12')
                self.bottom_status.config(text="⏳ Đang tải AuraFace model từ Hugging Face...")
                
                # Download model
                snapshot_download("fal/AuraFace-v1", local_dir="models/auraface")
                
                # Initialize FaceAnalysis
                self.face_app = FaceAnalysis(name="auraface", providers=["CPUExecutionProvider"], root=".")
                self.face_app.prepare(ctx_id=0, det_size=(640, 640))
                
                self.model_status.config(text="✅ Model sẵn sàng", fg='#27ae60')
                self.bottom_status.config(text="✅ AuraFace model đã được khởi tạo thành công")
                
            except Exception as e:
                self.model_status.config(text="❌ Lỗi khởi tạo", fg='#e74c3c')
                self.bottom_status.config(text=f"❌ Lỗi: {str(e)}")
                messagebox.showerror("Lỗi", f"Không thể khởi tạo model: {str(e)}")
                
        threading.Thread(target=init_model, daemon=True).start()
        
    def register_face(self):
        """Đăng ký khuôn mặt"""
        if not self.face_app:
            messagebox.showwarning("Cảnh báo", "Vui lòng khởi tạo model trước!")
            return
            
        if not self.current_frame is not None:
            messagebox.showwarning("Cảnh báo", "Không có hình ảnh từ camera!")
            return
            
        try:
            frame = cv2.flip(self.current_frame, 1)
            faces = self.face_app.get(frame)
            
            if faces:
                self.registered_embedding = faces[0].normed_embedding
                np.save('registered_face.npy', self.registered_embedding)
                
                self.register_status.config(text="✅ Đã đăng ký", fg='#27ae60')
                self.bottom_status.config(text="✅ Đã đăng ký khuôn mặt thành công")
                messagebox.showinfo("Thành công", "Đã đăng ký khuôn mặt thành công!")
            else:
                messagebox.showwarning("Cảnh báo", "Không phát hiện khuôn mặt! Vui lòng thử lại.")
                
        except Exception as e:
            messagebox.showerror("Lỗi", f"Lỗi đăng ký khuôn mặt: {str(e)}")
            
    def load_registered_face(self):
        """Tải khuôn mặt đã đăng ký từ file"""
        try:
            if os.path.exists('registered_face.npy'):
                self.registered_embedding = np.load('registered_face.npy')
                self.register_status.config(text="✅ Đã tải từ file", fg='#27ae60')
                self.bottom_status.config(text="✅ Đã tải khuôn mặt đã đăng ký từ file")
                messagebox.showinfo("Thành công", "Đã tải khuôn mặt từ file thành công!")
            else:
                messagebox.showwarning("Cảnh báo", "Không tìm thấy file khuôn mặt đã đăng ký!")
        except Exception as e:
            messagebox.showerror("Lỗi", f"Lỗi tải file: {str(e)}")
            
    def update_threshold(self, value):
        """Cập nhật ngưỡng nhận diện"""
        self.threshold = float(value)
        # Update label in settings frame
        for widget in self.root.winfo_children():
            if isinstance(widget, tk.Frame):
                for child in widget.winfo_children():
                    if isinstance(child, tk.Frame):
                        for grandchild in child.winfo_children():
                            if isinstance(grandchild, tk.LabelFrame) and "Cài Đặt" in grandchild.cget("text"):
                                for item in grandchild.winfo_children():
                                    if isinstance(item, tk.Label) and "Ngưỡng" in item.cget("text"):
                                        item.config(text=f"Ngưỡng: {self.threshold:.2f}")
                                        
    def toggle_recognition(self):
        """Bật/tắt nhận diện"""
        if not self.face_app:
            messagebox.showwarning("Cảnh báo", "Vui lòng khởi tạo model trước!")
            return
            
        if self.registered_embedding is None:
            messagebox.showwarning("Cảnh báo", "Vui lòng đăng ký khuôn mặt trước!")
            return
            
        self.is_running = not self.is_running
        
        if self.is_running:
            self.recognition_btn.config(text="⏸️ Dừng Nhận Diện", bg='#e74c3c')
            self.status_label.config(text="🔍 Đang nhận diện...", fg='#27ae60')
            self.bottom_status.config(text="🔍 Nhận diện khuôn mặt đang hoạt động")
        else:
            self.recognition_btn.config(text="▶️ Bắt Đầu Nhận Diện", bg='#27ae60')
            self.status_label.config(text="⏸️ Đã dừng", fg='#95a5a6')
            self.bottom_status.config(text="⏸️ Đã dừng nhận diện")
            
    def process_recognition(self, frame):
        """Xử lý nhận diện khuôn mặt"""
        try:
            faces = self.face_app.get(frame)
            
            for face in faces:
                bbox = face.bbox.astype(int)
                similarity = np.dot(self.registered_embedding, face.normed_embedding)
                
                if similarity >= self.threshold:
                    color = (0, 255, 0)  # Green
                    label = f"✅ ĐÚNG ({similarity:.3f})"
                    status_text = "✅ AUTHORIZED"
                else:
                    color = (0, 0, 255)  # Red  
                    label = f"❌ SAI ({similarity:.3f})"
                    status_text = "❌ UNAUTHORIZED"
                
                # Draw bounding box
                cv2.rectangle(frame, (bbox[0], bbox[1]), (bbox[2], bbox[3]), color, 2)
                
                # Draw label
                cv2.putText(frame, label, (bbox[0], bbox[1]-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
                
                # Draw additional info
                cv2.putText(frame, f"Confidence: {face.det_score:.2f}", 
                           (bbox[0], bbox[3]+20), cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
                
                # Update status
                self.status_label.config(text=status_text, 
                                       fg='#27ae60' if similarity >= self.threshold else '#e74c3c')
                
        except Exception as e:
            print(f"Recognition error: {e}")
            
        return frame
        
    def run(self):
        """Chạy ứng dụng"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()
        
    def on_closing(self):
        """Xử lý khi đóng ứng dụng"""
        if self.cap:
            self.cap.release()
        cv2.destroyAllWindows()
        self.root.destroy()

def main():
    """Hàm main"""
    try:
        app = AuraFaceGUI()
        app.run()
    except Exception as e:
        print(f"Lỗi: {e}")

if __name__ == "__main__":
    main()
