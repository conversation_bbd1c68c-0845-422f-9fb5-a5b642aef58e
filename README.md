# 🎭 AuraFace - Hệ Thống Nhận Diện <PERSON>huôn Mặt

Hệ thống nhận diện khuôn mặt tiên tiến sử dụng AuraFace model từ Hugging Face với khả năng chọn camera linh hoạt.

## ✨ Tính Năng

- 🎯 **Nhận diện chính xác** với AuraFace model tiên tiến
- 📹 **Hỗ trợ đa camera** - tự động phát hiện và cho phép chọn camera
- 🔧 **Linh hoạt** - điều chỉnh ngưỡng nhận diện trong lúc chạy
- 💾 **Lưu trữ thông minh** - tự động lưu/tải khuôn mặt đã đăng ký
- 🖥️ **Giao diện trực quan** - hiển thị thông tin chi tiết (tuổi, giới t<PERSON>h, độ tin cậy)
- ⚡ **Hiệ<PERSON> suất cao** - tối ưu cho macOS với AVFoundation backend

## 🚀 Cài Đặt

### Yêu <PERSON>hống
- Python 3.8+
- macOS (đã test) / Linux / Windows
- Camera (built-in hoặc external)

### Cài Đặt Dependencies

```bash
pip install opencv-python insightface huggingface_hub numpy
```

## 📖 Cách Sử Dụng

### Chạy Chương Trình

```bash
python auraface_recognition.py
```

### Quy Trình Sử Dụng

1. **Phát hiện Camera**: Chương trình tự động tìm tất cả camera có sẵn
2. **Chọn Camera**: Chọn camera muốn sử dụng từ danh sách
3. **Khởi tạo Model**: AuraFace model sẽ được tải (1-2 phút lần đầu)
4. **Đăng ký Khuôn Mặt**: 
   - Nhấn SPACE để chụp ảnh đăng ký
   - Đảm bảo khuôn mặt rõ ràng và đủ sáng
5. **Nhận Diện**: Hệ thống sẽ nhận diện real-time
   - Khung xanh = Đúng người
   - Khung đỏ = Sai người
   - Nhấn 'R' để thay đổi ngưỡng

### Phím Tắt

- **SPACE**: Chụp ảnh đăng ký (trong chế độ đăng ký)
- **ESC**: Thoát chương trình
- **R**: Thay đổi ngưỡng nhận diện (trong chế độ nhận diện)

## 🎯 Cấu Hình

### Ngưỡng Nhận Diện
- **0.6** (khuyến nghị): Cân bằng giữa độ chính xác và false positive
- **0.7-0.8**: Nghiêm ngặt hơn, ít false positive
- **0.4-0.5**: Lỏng hơn, nhiều false positive

### Camera Được Hỗ Trợ
- **Camera 0**: Thường là camera chính/external (Sony, USB, etc.)
- **Camera 1**: Thường là camera built-in MacBook
- **Camera 2+**: Các camera bổ sung

## 📁 Cấu Trúc Dự Án

```
auraface-project/
├── auraface_recognition.py    # File chính
├── README.md                  # Hướng dẫn này
├── registered_face.npy        # Khuôn mặt đã đăng ký (tự động tạo)
└── models/                    # AuraFace model (tự động tải)
    └── auraface/
```

## 🔧 Tùy Chỉnh

### Thay Đổi Độ Phân Giải Camera

Trong `CameraManager.open_camera()`:
```python
cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)   # Thay đổi width
cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)   # Thay đổi height
```

### Thay Đổi Model Detection Size

Trong `AuraFaceRecognizer.init_auraface()`:
```python
self.face_app.prepare(ctx_id=0, det_size=(512, 512))  # Nhỏ hơn = nhanh hơn
```

## 🐛 Xử Lý Lỗi

### Camera Không Hoạt Động
1. Kiểm tra camera có đang được sử dụng bởi ứng dụng khác
2. Thử rút và cắm lại camera USB
3. Khởi động lại ứng dụng

### Model Tải Chậm
- Lần đầu tiên sẽ tải model từ Hugging Face (1-2 phút)
- Các lần sau sẽ nhanh hơn vì đã cache

### Nhận Diện Không Chính Xác
1. Đảm bảo ánh sáng đủ khi đăng ký
2. Điều chỉnh ngưỡng (nhấn 'R')
3. Đăng ký lại khuôn mặt với điều kiện tốt hơn

## 🎉 Tính Năng Nâng Cao

- **Đa Camera**: Dễ dàng chuyển đổi giữa các camera
- **Lưu Trữ**: Tự động lưu embedding, không cần đăng ký lại
- **Real-time Info**: Hiển thị tuổi, giới tính, độ tin cậy
- **Flexible Threshold**: Điều chỉnh ngưỡng trong lúc chạy

## 📞 Hỗ Trợ

Nếu gặp vấn đề:
1. Kiểm tra camera hoạt động với ứng dụng khác
2. Đảm bảo đã cài đặt đúng dependencies
3. Kiểm tra Python version >= 3.8

---

**Phát triển bởi**: AuraFace Team  
**Model**: [fal/AuraFace-v1](https://huggingface.co/fal/AuraFace-v1)  
**Framework**: OpenCV + InsightFace + Hugging Face
