from huggingface_hub import snapshot_download
from insightface.app import FaceAnalysis
import numpy as np
import cv2
import os

# Tải model từ Hugging Face
snapshot_download(
    "fal/AuraFace-v1",
    local_dir="models/auraface",
)

# Khởi tạo FaceAnalysis với AuraFace, dùng CPU
face_app = FaceAnalysis(
    name="auraface",
    providers=["CPUExecutionProvider"],  # Chỉ CPU cho Mac Intel
    root=".",
)

# Chuẩn bị model (có thể mất thời gian lần đầu)
face_app.prepare(ctx_id=0, det_size=(640, 640))

def list_cameras():
    """Liệt kê tất cả camera có sẵn"""
    cameras = []
    for i in range(10):  # Kiểm tra tối đa 10 camera
        cap = cv2.VideoCapture(i)
        if cap.isOpened():
            ret, frame = cap.read()
            if ret:
                cameras.append(i)
            cap.release()
    return cameras

def select_camera():
    """Cho phép người dùng chọn camera"""
    cameras = list_cameras()

    if not cameras:
        print("Không tìm thấy camera nào!")
        return None

    print(f"Tìm thấy {len(cameras)} camera:")
    for i, cam_id in enumerate(cameras):
        print(f"  {i+1}. Camera {cam_id}")

    while True:
        try:
            choice = input(f"Chọn camera (1-{len(cameras)}): ")
            choice_idx = int(choice) - 1
            if 0 <= choice_idx < len(cameras):
                return cameras[choice_idx]
            else:
                print(f"Vui lòng chọn từ 1 đến {len(cameras)}")
        except ValueError:
            print("Vui lòng nhập số!")

def calculate_similarity(embedding1, embedding2):
    """Tính độ tương đồng cosine giữa hai embedding"""
    return np.dot(embedding1, embedding2)

def register_face():
    """Đăng ký khuôn mặt của bạn làm reference"""
    print("=== ĐĂNG KÝ KHUÔN MẶT CỦA BẠN ===")

    # Chọn camera
    camera_id = select_camera()
    if camera_id is None:
        return None

    print("Nhấn SPACE để chụp ảnh đăng ký, ESC để thoát")

    cap = cv2.VideoCapture(camera_id)
    if not cap.isOpened():
        print(f"Không thể mở camera {camera_id}!")
        return None

    registered_embedding = None

    while True:
        ret, frame = cap.read()
        if not ret:
            break

        # Flip frame để giống như nhìn gương
        frame = cv2.flip(frame, 1)

        # Phát hiện khuôn mặt
        faces = face_app.get(frame)

        # Vẽ khung khuôn mặt
        for face in faces:
            bbox = face.bbox.astype(int)
            cv2.rectangle(frame, (bbox[0], bbox[1]), (bbox[2], bbox[3]), (0, 255, 0), 2)
            cv2.putText(frame, f"Confidence: {face.det_score:.2f}",
                       (bbox[0], bbox[1]-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

        cv2.putText(frame, "Nhan SPACE de dang ky khuon mat, ESC de thoat",
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.imshow('Dang ky khuon mat', frame)

        key = cv2.waitKey(1) & 0xFF
        if key == 27:  # ESC
            break
        elif key == 32:  # SPACE
            if faces:
                registered_embedding = faces[0].normed_embedding
                print("Đã đăng ký khuôn mặt thành công!")
                # Lưu embedding vào file
                np.save('registered_face.npy', registered_embedding)
                break
            else:
                print("Không phát hiện khuôn mặt! Thử lại.")

    cap.release()
    cv2.destroyAllWindows()
    return registered_embedding

def recognize_face_realtime(registered_embedding, threshold=0.6):
    """Nhận diện khuôn mặt theo thời gian thực"""
    print("=== NHẬN DIỆN KHUÔN MẶT THEO THỜI GIAN THỰC ===")

    # Chọn camera
    camera_id = select_camera()
    if camera_id is None:
        return

    print("Nhấn ESC để thoát")

    cap = cv2.VideoCapture(camera_id)
    if not cap.isOpened():
        print(f"Không thể mở camera {camera_id}!")
        return

    while True:
        ret, frame = cap.read()
        if not ret:
            break

        # Flip frame để giống như nhìn gương
        frame = cv2.flip(frame, 1)

        # Phát hiện khuôn mặt
        faces = face_app.get(frame)

        for face in faces:
            bbox = face.bbox.astype(int)

            # Tính độ tương đồng
            similarity = calculate_similarity(registered_embedding, face.normed_embedding)

            # Xác định màu sắc và nhãn
            if similarity >= threshold:
                color = (0, 255, 0)  # Xanh lá - Đúng người
                label = f"DUNG NGUOI! ({similarity:.3f})"
            else:
                color = (0, 0, 255)  # Đỏ - Sai người
                label = f"SAI NGUOI! ({similarity:.3f})"

            # Vẽ khung và nhãn
            cv2.rectangle(frame, (bbox[0], bbox[1]), (bbox[2], bbox[3]), color, 2)
            cv2.putText(frame, label, (bbox[0], bbox[1]-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

            # Hiển thị thông tin thêm
            cv2.putText(frame, f"Confidence: {face.det_score:.2f}",
                       (bbox[0], bbox[3]+20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            cv2.putText(frame, f"Age: {face.age:.0f}, Gender: {'M' if face.gender == 1 else 'F'}",
                       (bbox[0], bbox[3]+40), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

        # Hiển thị hướng dẫn
        cv2.putText(frame, f"Nguong nhan dien: {threshold:.2f} - Nhan ESC de thoat",
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        cv2.imshow('Nhan dien khuon mat', frame)

        if cv2.waitKey(1) & 0xFF == 27:  # ESC
            break

    cap.release()
    cv2.destroyAllWindows()

def main():
    """Chương trình chính"""
    print("=== CHƯƠNG TRÌNH NHẬN DIỆN KHUÔN MẶT ===")

    # Kiểm tra xem đã có embedding đăng ký chưa
    if os.path.exists('registered_face.npy'):
        print("Tìm thấy khuôn mặt đã đăng ký.")
        choice = input("Bạn muốn: (1) Sử dụng khuôn mặt đã đăng ký, (2) Đăng ký lại? [1/2]: ")

        if choice == '2':
            registered_embedding = register_face()
        else:
            registered_embedding = np.load('registered_face.npy')
            print("Đã tải khuôn mặt đã đăng ký.")
    else:
        print("Chưa có khuôn mặt đăng ký. Cần đăng ký trước.")
        registered_embedding = register_face()

    if registered_embedding is not None:
        # Cho phép điều chỉnh ngưỡng
        threshold = float(input("Nhập ngưỡng nhận diện (0.0-1.0, khuyến nghị 0.6): ") or "0.6")
        recognize_face_realtime(registered_embedding, threshold)
    else:
        print("Không thể đăng ký khuôn mặt. Thoát chương trình.")

if __name__ == "__main__":
    main()