#!/usr/bin/env python3
"""
🎭 AuraFace - Phiên Bản Đơn Giản
Sử dụng OpenCV window thay vì Tkinter để tránh vấn đề hiển thị
"""

from huggingface_hub import snapshot_download
from insightface.app import FaceAnalysis
import numpy as np
import cv2
import os

def init_auraface():
    """Khởi tạo AuraFace model"""
    print("🎭 KHỞI TẠO AURAFACE")
    print("=" * 50)
    
    print("⏳ Đang tải model từ Hugging Face...")
    snapshot_download(
        "fal/AuraFace-v1",
        local_dir="models/auraface",
    )
    
    print("⏳ Đang khởi tạo FaceAnalysis...")
    face_app = FaceAnalysis(
        name="auraface",
        providers=["CPUExecutionProvider"],
        root=".",
    )
    
    print("⏳ Đang chuẩn bị model...")
    face_app.prepare(ctx_id=0, det_size=(640, 640))
    
    print("✅ AuraFace đã sẵn sàng!")
    return face_app

def register_face(face_app):
    """Đăng ký khuôn mặt"""
    print("\n📝 ĐĂNG KÝ KHUÔN MẶT")
    print("=" * 50)
    print("📋 Hướng dẫn:")
    print("  • Nhấn SPACE để chụp ảnh đăng ký")
    print("  • Nhấn ESC để thoát")
    print("  • Đảm bảo khuôn mặt rõ ràng và đủ sáng")

    # Thử nhiều cách để mở camera built-in
    cap = None
    camera_found = False

    # Thử các camera ID khác nhau
    for camera_id in [1, 0, 2]:
        print(f"🔍 Thử camera {camera_id}...")
        try:
            # Thử với AVFoundation backend trực tiếp
            test_cap = cv2.VideoCapture(camera_id, cv2.CAP_AVFOUNDATION)
            if test_cap.isOpened():
                ret, frame = test_cap.read()
                if ret and frame is not None:
                    print(f"✅ Camera {camera_id} hoạt động!")
                    cap = test_cap
                    camera_found = True
                    break
                else:
                    test_cap.release()
            else:
                # Thử cách thông thường
                test_cap = cv2.VideoCapture(camera_id)
                if test_cap.isOpened():
                    ret, frame = test_cap.read()
                    if ret and frame is not None:
                        print(f"✅ Camera {camera_id} hoạt động!")
                        cap = test_cap
                        camera_found = True
                        break
                    else:
                        test_cap.release()
        except Exception as e:
            print(f"❌ Lỗi camera {camera_id}: {e}")
            continue

    if not camera_found:
        print("❌ Không thể mở camera nào!")
        return None
    
    # Cài đặt độ phân giải
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    
    print("📷 Camera đã sẵn sàng. Cửa sổ video sẽ mở...")
    
    registered_embedding = None
    
    while True:
        ret, frame = cap.read()
        if not ret:
            print("❌ Không thể đọc frame từ camera!")
            break
        
        # Flip frame để giống như nhìn gương
        frame = cv2.flip(frame, 1)
        
        # Phát hiện khuôn mặt
        faces = face_app.get(frame)
        
        # Vẽ khung khuôn mặt
        for face in faces:
            bbox = face.bbox.astype(int)
            cv2.rectangle(frame, (bbox[0], bbox[1]), (bbox[2], bbox[3]), (0, 255, 0), 2)
            cv2.putText(frame, f"Confidence: {face.det_score:.2f}", 
                       (bbox[0], bbox[1]-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        
        # Hiển thị hướng dẫn
        cv2.putText(frame, "Nhan SPACE de dang ky, ESC de thoat", 
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(frame, f"So khuon mat: {len(faces)}", 
                   (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        
        # Hiển thị frame
        cv2.imshow('Dang ky khuon mat - AuraFace', frame)
        
        key = cv2.waitKey(1) & 0xFF
        if key == 27:  # ESC
            print("❌ Đã hủy đăng ký")
            break
        elif key == 32:  # SPACE
            if faces:
                registered_embedding = faces[0].normed_embedding
                print("✅ Đã đăng ký khuôn mặt thành công!")
                # Lưu embedding vào file
                np.save('registered_face.npy', registered_embedding)
                print("💾 Đã lưu thông tin khuôn mặt vào file")
                break
            else:
                print("❌ Không phát hiện khuôn mặt! Thử lại.")
    
    cap.release()
    cv2.destroyAllWindows()
    return registered_embedding

def recognize_face_realtime(face_app, registered_embedding, threshold=0.6):
    """Nhận diện khuôn mặt theo thời gian thực"""
    print("\n🔍 NHẬN DIỆN KHUÔN MẶT THEO THỜI GIAN THỰC")
    print("=" * 50)
    print(f"🎯 Ngưỡng nhận diện: {threshold:.2f}")
    print("📋 Hướng dẫn:")
    print("  • Nhấn ESC để thoát")
    print("  • Nhấn 'r' để điều chỉnh ngưỡng")
    print("  • Xanh lá = Đúng người, Đỏ = Sai người")

    # Thử nhiều cách để mở camera built-in
    cap = None
    camera_found = False

    # Thử các camera ID khác nhau
    for camera_id in [1, 0, 2]:
        print(f"🔍 Thử camera {camera_id}...")
        try:
            # Thử với AVFoundation backend trực tiếp
            test_cap = cv2.VideoCapture(camera_id, cv2.CAP_AVFOUNDATION)
            if test_cap.isOpened():
                ret, frame = test_cap.read()
                if ret and frame is not None:
                    print(f"✅ Camera {camera_id} hoạt động!")
                    cap = test_cap
                    camera_found = True
                    break
                else:
                    test_cap.release()
            else:
                # Thử cách thông thường
                test_cap = cv2.VideoCapture(camera_id)
                if test_cap.isOpened():
                    ret, frame = test_cap.read()
                    if ret and frame is not None:
                        print(f"✅ Camera {camera_id} hoạt động!")
                        cap = test_cap
                        camera_found = True
                        break
                    else:
                        test_cap.release()
        except Exception as e:
            print(f"❌ Lỗi camera {camera_id}: {e}")
            continue

    if not camera_found:
        print("❌ Không thể mở camera nào!")
        return
    
    # Cài đặt độ phân giải
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    
    print("📷 Camera đã sẵn sàng. Cửa sổ video sẽ mở...")
    
    while True:
        ret, frame = cap.read()
        if not ret:
            print("❌ Không thể đọc frame từ camera!")
            break
        
        # Flip frame để giống như nhìn gương
        frame = cv2.flip(frame, 1)
        
        # Phát hiện khuôn mặt
        faces = face_app.get(frame)
        
        for face in faces:
            bbox = face.bbox.astype(int)
            
            # Tính độ tương đồng
            similarity = np.dot(registered_embedding, face.normed_embedding)
            
            # Xác định màu sắc và nhãn
            if similarity >= threshold:
                color = (0, 255, 0)  # Xanh lá - Đúng người
                label = f"DUNG NGUOI! ({similarity:.3f})"
                status = "✅ AUTHORIZED"
            else:
                color = (0, 0, 255)  # Đỏ - Sai người
                label = f"SAI NGUOI! ({similarity:.3f})"
                status = "❌ UNAUTHORIZED"
            
            # Vẽ khung và nhãn
            cv2.rectangle(frame, (bbox[0], bbox[1]), (bbox[2], bbox[3]), color, 2)
            cv2.putText(frame, label, (bbox[0], bbox[1]-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
            
            # Hiển thị thông tin thêm
            cv2.putText(frame, f"Confidence: {face.det_score:.2f}", 
                       (bbox[0], bbox[3]+20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            cv2.putText(frame, f"Age: {face.age:.0f}, Gender: {'M' if face.gender == 1 else 'F'}", 
                       (bbox[0], bbox[3]+40), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            
            # In trạng thái ra console
            print(f"\r{status} - Similarity: {similarity:.3f}", end="", flush=True)
        
        # Hiển thị hướng dẫn
        cv2.putText(frame, f"Nguong: {threshold:.2f} - ESC: thoat, R: thay doi nguong", 
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
        cv2.putText(frame, f"So khuon mat: {len(faces)}", 
                   (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        
        # Hiển thị frame
        cv2.imshow('Nhan dien khuon mat - AuraFace', frame)
        
        key = cv2.waitKey(1) & 0xFF
        if key == 27:  # ESC
            break
        elif key == ord('r') or key == ord('R'):  # R để thay đổi ngưỡng
            print(f"\nNgưỡng hiện tại: {threshold:.2f}")
            try:
                new_threshold = float(input("Nhập ngưỡng mới (0.0-1.0): "))
                if 0.0 <= new_threshold <= 1.0:
                    threshold = new_threshold
                    print(f"✅ Đã cập nhật ngưỡng: {threshold:.2f}")
                else:
                    print("❌ Ngưỡng phải từ 0.0 đến 1.0")
            except ValueError:
                print("❌ Vui lòng nhập số hợp lệ")
    
    cap.release()
    cv2.destroyAllWindows()
    print("\n✅ Đã thoát chương trình nhận diện")

def main():
    """Chương trình chính"""
    print("🎭 AURAFACE - HỆ THỐNG NHẬN DIỆN KHUÔN MẶT")
    print("=" * 60)
    
    # Khởi tạo AuraFace
    face_app = init_auraface()
    
    # Kiểm tra xem đã có embedding đăng ký chưa
    if os.path.exists('registered_face.npy'):
        print("\n📁 Tìm thấy khuôn mặt đã đăng ký.")
        choice = input("🤔 Bạn muốn: (1) Sử dụng khuôn mặt đã đăng ký, (2) Đăng ký lại? [1/2]: ")
        
        if choice == '2':
            registered_embedding = register_face(face_app)
        else:
            registered_embedding = np.load('registered_face.npy')
            print("✅ Đã tải khuôn mặt đã đăng ký.")
    else:
        print("\n📝 Chưa có khuôn mặt đăng ký. Cần đăng ký trước.")
        registered_embedding = register_face(face_app)
    
    if registered_embedding is not None:
        # Cho phép điều chỉnh ngưỡng
        threshold_input = input("\n🎯 Nhập ngưỡng nhận diện (0.0-1.0, khuyến nghị 0.6): ")
        try:
            threshold = float(threshold_input) if threshold_input else 0.6
        except ValueError:
            threshold = 0.6
            print("⚠️ Sử dụng ngưỡng mặc định: 0.6")
        
        recognize_face_realtime(face_app, registered_embedding, threshold)
    else:
        print("❌ Không thể đăng ký khuôn mặt. Thoát chương trình.")

if __name__ == "__main__":
    main()
