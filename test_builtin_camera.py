#!/usr/bin/env python3
"""
Test camera built-in của MacBook
"""

import cv2
import numpy as np

def test_builtin_camera():
    """Test camera built-in"""
    print("🔍 Test camera built-in MacBook...")
    
    # Thử các camera ID khác nhau
    for camera_id in [0, 1, 2]:
        print(f"\n📹 Thử camera ID {camera_id}...")
        
        cap = cv2.VideoCapture(camera_id)
        
        if cap.isOpened():
            # Thử đọc frame
            ret, frame = cap.read()
            if ret and frame is not None:
                print(f"✅ Camera {camera_id} hoạt động!")
                print(f"   Kích thước: {frame.shape}")
                
                # Hiển thị video
                print("   Nhấn 'q' để thoát, 's' để chụp ảnh test")
                
                while True:
                    ret, frame = cap.read()
                    if not ret:
                        break
                    
                    # Flip frame
                    frame = cv2.flip(frame, 1)
                    
                    # Thêm text
                    cv2.putText(frame, f"Camera {camera_id} - <PERSON>han 'q' de thoat, 's' de chup", 
                               (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                    
                    cv2.imshow(f'Test Camera {camera_id}', frame)
                    
                    key = cv2.waitKey(1) & 0xFF
                    if key == ord('q'):
                        break
                    elif key == ord('s'):
                        cv2.imwrite(f'test_camera_{camera_id}.jpg', frame)
                        print(f"   📸 Đã lưu ảnh test_camera_{camera_id}.jpg")
                
                cap.release()
                cv2.destroyAllWindows()
                return camera_id
            else:
                print(f"❌ Camera {camera_id} không đọc được frame")
        else:
            print(f"❌ Camera {camera_id} không mở được")
        
        if cap.isOpened():
            cap.release()
    
    print("❌ Không tìm thấy camera nào hoạt động!")
    return None

if __name__ == "__main__":
    working_camera = test_builtin_camera()
    if working_camera is not None:
        print(f"\n✅ Camera {working_camera} hoạt động tốt!")
        print("Bạn có thể sử dụng camera này cho ứng dụng nhận diện khuôn mặt.")
    else:
        print("\n❌ Không có camera nào hoạt động!")
