#!/usr/bin/env python3
"""
🎭 AuraFace - Hệ Thống <PERSON>ận Di<PERSON> Mặt Hoàn Chỉnh
Phiên bản: 1.0
T<PERSON><PERSON> gi<PERSON>: AI Assistant
M<PERSON> tả: Ứng dụng nhận diện khuôn mặt sử dụng AuraFace model với giao diện đồ họa
"""

import tkinter as tk
from tkinter import ttk, messagebox
from huggingface_hub import snapshot_download
from insightface.app import FaceAnalysis
import numpy as np
import cv2
import os
import threading
from PIL import Image, ImageTk
import time

class AuraFaceApp:
    def __init__(self, root):
        self.root = root
        self.root.title("🎭 AuraFace - Nhận Diện Khuôn Mặt")
        self.root.geometry("900x700")
        self.root.configure(bg='#f0f0f0')
        
        # Variables
        self.face_app = None
        self.registered_embedding = None
        self.cap = None
        self.is_running = False
        self.camera_id = 0  # Sony Camera (Imaging Edge)
        
        # Setup GUI
        self.setup_gui()
        
        # Initialize model
        self.init_model()
    
    def setup_gui(self):
        """Thiết lập giao diện người dùng"""
        # Main container
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = tk.Label(main_frame, text="🎭 AuraFace - Nhận Diện Khuôn Mặt", 
                              font=("Arial", 20, "bold"), bg='#f0f0f0', fg='#2c3e50')
        title_label.pack(pady=(0, 20))
        
        # Status frame
        status_frame = ttk.LabelFrame(main_frame, text="📊 Trạng Thái Hệ Thống", padding="10")
        status_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.status_label = tk.Label(status_frame, text="⏳ Đang khởi tạo hệ thống...", 
                                    font=("Arial", 11), bg='white', fg='#e74c3c')
        self.status_label.pack(fill=tk.X, pady=5)
        
        # Camera info
        camera_frame = ttk.LabelFrame(main_frame, text="📹 Thông Tin Camera", padding="10")
        camera_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.camera_label = tk.Label(camera_frame, text="📷 Camera 0: Sony Camera (Imaging Edge) - 1280x720 @ 30fps", 
                                    font=("Arial", 10), bg='white', fg='#27ae60')
        self.camera_label.pack(fill=tk.X, pady=5)
        
        # Control buttons
        control_frame = ttk.LabelFrame(main_frame, text="🎛️ Điều Khiển", padding="15")
        control_frame.pack(fill=tk.X, pady=(0, 15))
        
        button_frame = tk.Frame(control_frame, bg='#f0f0f0')
        button_frame.pack()
        
        # Register button
        self.register_btn = tk.Button(button_frame, text="📝 Đăng Ký Khuôn Mặt", 
                                     command=self.register_face, state="disabled",
                                     font=("Arial", 11, "bold"), bg='#3498db', fg='white',
                                     padx=20, pady=10)
        self.register_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # Start recognition button
        self.recognize_btn = tk.Button(button_frame, text="🔍 Bắt Đầu Nhận Diện", 
                                      command=self.start_recognition, state="disabled",
                                      font=("Arial", 11, "bold"), bg='#27ae60', fg='white',
                                      padx=20, pady=10)
        self.recognize_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # Stop button
        self.stop_btn = tk.Button(button_frame, text="⏹️ Dừng", 
                                 command=self.stop_recognition, state="disabled",
                                 font=("Arial", 11, "bold"), bg='#e74c3c', fg='white',
                                 padx=20, pady=10)
        self.stop_btn.pack(side=tk.LEFT)
        
        # Settings
        settings_frame = ttk.LabelFrame(main_frame, text="⚙️ Cài Đặt Nhận Diện", padding="15")
        settings_frame.pack(fill=tk.X, pady=(0, 15))
        
        threshold_frame = tk.Frame(settings_frame, bg='#f0f0f0')
        threshold_frame.pack(fill=tk.X)
        
        tk.Label(threshold_frame, text="🎯 Ngưỡng nhận diện:", 
                font=("Arial", 10), bg='#f0f0f0').pack(side=tk.LEFT)
        
        self.threshold_var = tk.DoubleVar(value=0.6)
        threshold_scale = tk.Scale(threshold_frame, from_=0.3, to=0.9, 
                                  variable=self.threshold_var, orient=tk.HORIZONTAL,
                                  resolution=0.01, length=200, bg='#f0f0f0')
        threshold_scale.pack(side=tk.LEFT, padx=(10, 0))
        
        self.threshold_label = tk.Label(threshold_frame, text="0.60", 
                                       font=("Arial", 10, "bold"), bg='#f0f0f0', fg='#2c3e50')
        self.threshold_label.pack(side=tk.LEFT, padx=(10, 0))
        
        threshold_scale.configure(command=self.update_threshold_label)
        
        # Video display
        video_frame = ttk.LabelFrame(main_frame, text="📺 Video Trực Tiếp", padding="10")
        video_frame.pack(fill=tk.BOTH, expand=True)
        
        self.video_label = tk.Label(video_frame, text="🎥 Chưa có video\nNhấn 'Bắt Đầu Nhận Diện' để bắt đầu", 
                                   font=("Arial", 14), bg='black', fg='white', 
                                   width=60, height=20)
        self.video_label.pack(expand=True, fill=tk.BOTH, padx=5, pady=5)
        
        # Info panel
        info_frame = ttk.LabelFrame(main_frame, text="ℹ️ Thông Tin", padding="10")
        info_frame.pack(fill=tk.X, pady=(15, 0))
        
        info_text = """
🔹 Hướng dẫn sử dụng:
   1. Đợi hệ thống khởi tạo hoàn tất
   2. Nhấn 'Đăng Ký Khuôn Mặt' để đăng ký khuôn mặt của bạn
   3. Nhấn 'Bắt Đầu Nhận Diện' để bắt đầu nhận diện
   4. Điều chỉnh ngưỡng nhận diện nếu cần thiết
        """
        
        info_label = tk.Label(info_frame, text=info_text, font=("Arial", 9), 
                             bg='#f0f0f0', fg='#34495e', justify=tk.LEFT)
        info_label.pack(fill=tk.X)
    
    def init_model(self):
        """Khởi tạo model AuraFace"""
        def load_model():
            try:
                self.update_status("⏳ Đang tải model AuraFace từ Hugging Face...", '#e67e22')
                
                # Download model
                snapshot_download(
                    "fal/AuraFace-v1",
                    local_dir="models/auraface",
                )
                
                self.update_status("⏳ Đang khởi tạo FaceAnalysis...", '#e67e22')
                
                # Initialize FaceAnalysis
                self.face_app = FaceAnalysis(
                    name="auraface",
                    providers=["CPUExecutionProvider"],
                    root=".",
                )
                
                self.update_status("⏳ Đang chuẩn bị model...", '#e67e22')
                self.face_app.prepare(ctx_id=0, det_size=(640, 640))
                
                # Check for existing registered face
                if os.path.exists('registered_face.npy'):
                    self.registered_embedding = np.load('registered_face.npy')
                    self.update_status("✅ Hệ thống sẵn sàng! Đã có khuôn mặt đăng ký.", '#27ae60')
                    self.root.after(0, lambda: self.recognize_btn.config(state="normal"))
                else:
                    self.update_status("✅ Hệ thống sẵn sàng! Cần đăng ký khuôn mặt trước.", '#f39c12')
                
                self.root.after(0, lambda: self.register_btn.config(state="normal"))
                
            except Exception as e:
                error_msg = f"❌ Lỗi khởi tạo: {str(e)}"
                self.update_status(error_msg, '#e74c3c')
                messagebox.showerror("Lỗi", f"Không thể khởi tạo model:\n{str(e)}")
        
        threading.Thread(target=load_model, daemon=True).start()
    
    def update_status(self, message, color='#2c3e50'):
        """Cập nhật trạng thái"""
        def update():
            self.status_label.config(text=message, fg=color)
        self.root.after(0, update)
    
    def update_threshold_label(self, value):
        """Cập nhật label ngưỡng"""
        self.threshold_label.config(text=f"{float(value):.2f}")
    
    def register_face(self):
        """Đăng ký khuôn mặt"""
        if not self.face_app:
            messagebox.showerror("Lỗi", "Model chưa được khởi tạo!")
            return
        
        # Create registration window
        reg_window = tk.Toplevel(self.root)
        reg_window.title("📝 Đăng Ký Khuôn Mặt")
        reg_window.geometry("800x600")
        reg_window.configure(bg='#f0f0f0')
        
        # Instructions
        instructions = tk.Label(reg_window, 
                               text="📋 Hướng dẫn:\n• Nhìn thẳng vào camera\n• Nhấn SPACE để chụp ảnh đăng ký\n• Nhấn ESC để thoát",
                               font=("Arial", 12), bg='#f0f0f0', fg='#2c3e50')
        instructions.pack(pady=15)
        
        # Video display
        reg_video_label = tk.Label(reg_window, text="📷 Đang khởi động camera...", 
                                  font=("Arial", 14), bg='black', fg='white')
        reg_video_label.pack(expand=True, fill=tk.BOTH, padx=20, pady=20)
        
        # Start registration camera
        self.start_registration_camera(reg_video_label, reg_window)
    
    def start_registration_camera(self, video_label, window):
        """Bắt đầu camera cho đăng ký"""
        cap = cv2.VideoCapture(self.camera_id)
        if not cap.isOpened():
            messagebox.showerror("Lỗi", f"Không thể mở camera {self.camera_id}!")
            window.destroy()
            return
        
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        
        def update_frame():
            if not window.winfo_exists():
                cap.release()
                return
            
            ret, frame = cap.read()
            if ret:
                frame = cv2.flip(frame, 1)
                faces = self.face_app.get(frame)
                
                # Draw face rectangles
                for face in faces:
                    bbox = face.bbox.astype(int)
                    cv2.rectangle(frame, (bbox[0], bbox[1]), (bbox[2], bbox[3]), (0, 255, 0), 2)
                    cv2.putText(frame, f"Confidence: {face.det_score:.2f}", 
                               (bbox[0], bbox[1]-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                
                # Add instructions
                cv2.putText(frame, "Nhan SPACE de dang ky, ESC de thoat", 
                           (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                cv2.putText(frame, f"So khuon mat: {len(faces)}", 
                           (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
                
                # Convert and display
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                pil_image = Image.fromarray(frame_rgb)
                pil_image = pil_image.resize((640, 480), Image.Resampling.LANCZOS)
                photo = ImageTk.PhotoImage(pil_image)
                
                video_label.configure(image=photo, text="")
                video_label.image = photo
            
            window.after(33, update_frame)
        
        def on_key(event):
            if event.keysym == 'space':
                ret, frame = cap.read()
                if ret:
                    frame = cv2.flip(frame, 1)
                    faces = self.face_app.get(frame)
                    if faces:
                        self.registered_embedding = faces[0].normed_embedding
                        np.save('registered_face.npy', self.registered_embedding)
                        messagebox.showinfo("Thành công", "✅ Đã đăng ký khuôn mặt thành công!")
                        self.update_status("✅ Hệ thống sẵn sàng! Đã có khuôn mặt đăng ký.", '#27ae60')
                        self.recognize_btn.config(state="normal")
                        cap.release()
                        window.destroy()
                    else:
                        messagebox.showwarning("Cảnh báo", "❌ Không phát hiện khuôn mặt!")
            elif event.keysym == 'Escape':
                cap.release()
                window.destroy()
        
        window.bind('<KeyPress>', on_key)
        window.focus_set()
        update_frame()
    
    def start_recognition(self):
        """Bắt đầu nhận diện"""
        if self.registered_embedding is None:
            messagebox.showerror("Lỗi", "Chưa có khuôn mặt đăng ký!")
            return
        
        self.is_running = True
        self.register_btn.config(state="disabled")
        self.recognize_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        
        self.update_status("🔍 Đang nhận diện khuôn mặt...", '#3498db')
        
        self.cap = cv2.VideoCapture(self.camera_id)
        if not self.cap.isOpened():
            messagebox.showerror("Lỗi", f"Không thể mở camera {self.camera_id}!")
            self.stop_recognition()
            return
        
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        
        self.update_recognition_frame()
    
    def update_recognition_frame(self):
        """Cập nhật frame nhận diện"""
        if not self.is_running or not self.cap:
            return
        
        ret, frame = self.cap.read()
        if ret:
            frame = cv2.flip(frame, 1)
            faces = self.face_app.get(frame)
            threshold = self.threshold_var.get()
            
            recognition_status = "❌ Không phát hiện khuôn mặt"
            
            for face in faces:
                bbox = face.bbox.astype(int)
                similarity = np.dot(self.registered_embedding, face.normed_embedding)
                
                if similarity >= threshold:
                    color = (0, 255, 0)  # Green
                    label = f"DUNG NGUOI! ({similarity:.3f})"
                    recognition_status = f"✅ ĐÚNG NGƯỜI - Độ tương đồng: {similarity:.3f}"
                else:
                    color = (0, 0, 255)  # Red
                    label = f"SAI NGUOI! ({similarity:.3f})"
                    recognition_status = f"❌ SAI NGƯỜI - Độ tương đồng: {similarity:.3f}"
                
                cv2.rectangle(frame, (bbox[0], bbox[1]), (bbox[2], bbox[3]), color, 2)
                cv2.putText(frame, label, (bbox[0], bbox[1]-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
                
                # Additional info
                cv2.putText(frame, f"Age: {face.age:.0f}, Gender: {'M' if face.gender == 1 else 'F'}", 
                           (bbox[0], bbox[3]+20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            
            # Add info overlay
            cv2.putText(frame, f"Nguong: {threshold:.2f}", 
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            cv2.putText(frame, f"So khuon mat: {len(faces)}", 
                       (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
            
            # Convert and display
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(frame_rgb)
            pil_image = pil_image.resize((640, 480), Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(pil_image)
            
            self.video_label.configure(image=photo, text="")
            self.video_label.image = photo
            
            # Update status
            self.update_status(f"🔍 {recognition_status}", '#3498db')
        
        if self.is_running:
            self.root.after(33, self.update_recognition_frame)
    
    def stop_recognition(self):
        """Dừng nhận diện"""
        self.is_running = False
        if self.cap:
            self.cap.release()
            self.cap = None
        
        self.video_label.configure(image="", text="🎥 Đã dừng nhận diện\nNhấn 'Bắt Đầu Nhận Diện' để tiếp tục")
        self.video_label.image = None
        
        self.register_btn.config(state="normal")
        self.recognize_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        
        self.update_status("⏹️ Đã dừng nhận diện", '#e74c3c')

def main():
    """Hàm chính"""
    print("🎭 Khởi động AuraFace - Hệ Thống Nhận Diện Khuôn Mặt")
    print("=" * 60)
    
    root = tk.Tk()
    app = AuraFaceApp(root)
    
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("\n👋 Tạm biệt!")
    except Exception as e:
        print(f"❌ Lỗi: {e}")

if __name__ == "__main__":
    main()
